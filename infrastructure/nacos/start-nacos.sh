#!/bin/bash

# 启动Nacos服务器（单机模式） - macOS版本
# 自动检查和下载Nacos 2.3.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Nacos配置
NACOS_VERSION="2.3.0"
NACOS_URL="https://github.com/alibaba/nacos/releases/download/${NACOS_VERSION}/nacos-server-${NACOS_VERSION}.tar.gz"
SCRIPT_DIR="$(dirname "$0")"
NACOS_DIR="${SCRIPT_DIR}/nacos"
NACOS_JAR="${NACOS_DIR}/target/nacos-server.jar"

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Nacos是否完整安装
check_nacos_installation() {
    if [ ! -f "${NACOS_JAR}" ]; then
        log_warn "Nacos安装不完整，缺少 nacos-server.jar 文件"
        return 1
    fi

    if [ ! -d "${NACOS_DIR}/bin" ]; then
        log_warn "Nacos安装不完整，缺少 bin 目录"
        return 1
    fi

    if [ ! -f "${NACOS_DIR}/bin/startup.sh" ]; then
        log_warn "Nacos安装不完整，缺少启动脚本"
        return 1
    fi

    log_info "Nacos安装检查通过"
    return 0
}

# 下载并安装Nacos
download_and_install_nacos() {
    log_step "开始下载Nacos ${NACOS_VERSION}..."

    cd "${SCRIPT_DIR}"

    # 备份现有配置（如果存在）
    if [ -f "${NACOS_DIR}/conf/application.properties" ]; then
        log_info "备份现有配置文件..."
        cp "${NACOS_DIR}/conf/application.properties" "${SCRIPT_DIR}/application.properties.backup"
    fi

    # 下载Nacos
    log_info "正在下载 nacos-server-${NACOS_VERSION}.tar.gz..."
    if command -v curl >/dev/null 2>&1; then
        curl -L -O "${NACOS_URL}"
    elif command -v wget >/dev/null 2>&1; then
        wget "${NACOS_URL}"
    else
        log_error "未找到 curl 或 wget 命令，无法下载Nacos"
        exit 1
    fi

    # 解压Nacos
    log_info "正在解压Nacos..."
    if [ -d "${NACOS_DIR}" ]; then
        rm -rf "${NACOS_DIR}"
    fi
    tar -xzf "nacos-server-${NACOS_VERSION}.tar.gz"

    # 恢复配置文件
    if [ -f "${SCRIPT_DIR}/application.properties.backup" ]; then
        log_info "恢复配置文件..."
        cp "${SCRIPT_DIR}/application.properties.backup" "${NACOS_DIR}/conf/application.properties"
        rm -f "${SCRIPT_DIR}/application.properties.backup"
    fi

    # 清理下载文件
    rm -f "nacos-server-${NACOS_VERSION}.tar.gz"

    log_info "Nacos ${NACOS_VERSION} 安装完成"
}

# 启动Nacos
start_nacos() {
    log_step "启动Nacos服务器..."

    cd "${NACOS_DIR}/bin"

    # 设置JVM参数
    export JAVA_OPT="-server -Xms512m -Xmx512m -Xmn256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m"

    # 启动Nacos
    sh startup.sh -m standalone

    echo ""
    log_info "Nacos服务器已启动"
    echo "访问地址: http://localhost:8848/nacos"
    echo "默认用户名/密码: nacos/nacos"
}

# 主函数
main() {
    echo "=========================================="
    echo "      Nacos启动脚本 (macOS)"
    echo "=========================================="
    echo ""

    # 检查Nacos安装
    if ! check_nacos_installation; then
        log_step "自动下载和安装Nacos ${NACOS_VERSION}..."
        download_and_install_nacos
    fi

    # 启动Nacos
    start_nacos
}

# 运行主函数
main "$@"