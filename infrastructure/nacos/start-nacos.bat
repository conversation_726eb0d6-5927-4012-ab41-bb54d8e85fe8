@echo off
chcp 65001 >nul

REM 启动Nacos服务器（单机模式） - Windows版本
REM 自动检查和下载Nacos 2.3.0

setlocal enabledelayedexpansion

REM Nacos配置
set "NACOS_VERSION=2.3.0"
set "NACOS_URL=https://github.com/alibaba/nacos/releases/download/!NACOS_VERSION!/nacos-server-!NACOS_VERSION!.tar.gz"
set "SCRIPT_DIR=%~dp0"
set "NACOS_DIR=%SCRIPT_DIR%nacos"
set "NACOS_JAR=%NACOS_DIR%\target\nacos-server.jar"

REM 颜色定义（Windows控制台）
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

goto main

:log_info
echo %GREEN%[INFO]%NC% %~1
goto :eof

:log_warn
echo %YELLOW%[WARN]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:log_step
echo %BLUE%[STEP]%NC% %~1
goto :eof

REM 检查Nacos是否完整安装
:check_nacos_installation
if not exist "%NACOS_JAR%" (
    call :log_warn "Nacos安装不完整，缺少 nacos-server.jar 文件"
    exit /b 1
)

if not exist "%NACOS_DIR%\bin" (
    call :log_warn "Nacos安装不完整，缺少 bin 目录"
    exit /b 1
)

if not exist "%NACOS_DIR%\bin\startup.cmd" (
    call :log_warn "Nacos安装不完整，缺少启动脚本"
    exit /b 1
)

call :log_info "Nacos安装检查通过"
exit /b 0

REM 下载并安装Nacos
:download_and_install_nacos
call :log_step "开始下载Nacos %NACOS_VERSION%..."

cd /d "%SCRIPT_DIR%"

REM 备份现有配置（如果存在）
if exist "%NACOS_DIR%\conf\application.properties" (
    call :log_info "备份现有配置文件..."
    copy "%NACOS_DIR%\conf\application.properties" "%SCRIPT_DIR%application.properties.backup" >nul
)

REM 下载Nacos
call :log_info "正在下载 nacos-server-%NACOS_VERSION%.tar.gz..."

REM 使用PowerShell下载文件
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%NACOS_URL%' -OutFile 'nacos-server-%NACOS_VERSION%.tar.gz'}"

if %errorlevel% neq 0 (
    call :log_error "下载Nacos失败"
    pause
    exit /b 1
)

REM 解压Nacos（使用PowerShell）
call :log_info "正在解压Nacos..."
if exist "%NACOS_DIR%" (
    rmdir /s /q "%NACOS_DIR%"
)

REM 使用PowerShell解压tar.gz文件
powershell -Command "& {Add-Type -AssemblyName System.IO.Compression.FileSystem; $tarGz = '%SCRIPT_DIR%nacos-server-%NACOS_VERSION%.tar.gz'; $tempDir = '%SCRIPT_DIR%temp_extract'; if(Test-Path $tempDir) {Remove-Item $tempDir -Recurse -Force}; New-Item -ItemType Directory -Path $tempDir | Out-Null; $gzStream = New-Object System.IO.FileStream($tarGz, [System.IO.FileMode]::Open); $gzipStream = New-Object System.IO.Compression.GzipStream($gzStream, [System.IO.Compression.CompressionMode]::Decompress); $tarFile = Join-Path $tempDir 'nacos.tar'; $tarStream = New-Object System.IO.FileStream($tarFile, [System.IO.FileMode]::Create); $gzipStream.CopyTo($tarStream); $tarStream.Close(); $gzipStream.Close(); $gzStream.Close(); $shell = New-Object -ComObject Shell.Application; $tar = $shell.NameSpace($tarFile); $dest = $shell.NameSpace('%SCRIPT_DIR%'); $dest.CopyHere($tar.Items(), 4); Remove-Item $tempDir -Recurse -Force}"

if %errorlevel% neq 0 (
    call :log_error "解压Nacos失败，尝试使用7-Zip..."
    REM 尝试使用7-Zip（如果安装了）
    if exist "C:\Program Files\7-Zip\7z.exe" (
        "C:\Program Files\7-Zip\7z.exe" x "nacos-server-%NACOS_VERSION%.tar.gz" -so | "C:\Program Files\7-Zip\7z.exe" x -si -ttar
    ) else (
        call :log_error "无法解压Nacos文件，请手动下载并解压到nacos目录"
        pause
        exit /b 1
    )
)

REM 恢复配置文件
if exist "%SCRIPT_DIR%application.properties.backup" (
    call :log_info "恢复配置文件..."
    copy "%SCRIPT_DIR%application.properties.backup" "%NACOS_DIR%\conf\application.properties" >nul
    del "%SCRIPT_DIR%application.properties.backup"
)

REM 清理下载文件
del "nacos-server-%NACOS_VERSION%.tar.gz"

call :log_info "Nacos %NACOS_VERSION% 安装完成"
goto :eof

REM 启动Nacos
:start_nacos
call :log_step "启动Nacos服务器..."

cd /d "%NACOS_DIR%\bin"

REM 设置JVM参数
set "JAVA_OPT=-server -Xms512m -Xmx512m -Xmn256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m"

REM 启动Nacos
call startup.cmd -m standalone

echo.
call :log_info "Nacos服务器已启动"
echo 访问地址: http://localhost:8848/nacos
echo 默认用户名/密码: nacos/nacos
echo.
goto :eof

REM 主函数
:main
echo ==========================================
echo       Nacos启动脚本 (Windows)
echo ==========================================
echo.

REM 检查Nacos安装
call :check_nacos_installation
if %errorlevel% neq 0 (
    call :log_step "自动下载和安装Nacos %NACOS_VERSION%..."
    call :download_and_install_nacos
)

REM 启动Nacos
call :start_nacos

pause
