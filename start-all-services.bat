@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM SpringCloud微服务学习平台一键启动脚本 (Windows版本)
REM 作者: AI Assistant
REM 日期: 2025-08-02

REM 配置信息
set JAVA_HOME=C:\Program Files\Java\jdk-22
set MYSQL_HOST=***************
set MYSQL_PORT=3306
set MYSQL_USER=root
set MYSQL_PASSWORD=mysql

REM 颜色定义 (Windows 10/11 支持ANSI颜色)
set "RED=[31m"
set "GREEN=[32m"
set "YELLOW=[33m"
set "BLUE=[34m"
set "NC=[0m"

REM 日志函数
:log_info
echo %GREEN%[INFO]%NC% %~1
goto :eof

:log_warn
echo %YELLOW%[WARN]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:log_step
echo %BLUE%[STEP]%NC% %~1
goto :eof

REM 检查命令是否存在
:check_command
where %1 >nul 2>&1
if %errorlevel% neq 0 (
    call :log_error "%1 命令未找到，请先安装 %1"
    exit /b 1
)
exit /b 0

REM 检查端口是否被占用
:check_port
netstat -an | findstr ":%1 " | findstr "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    call :log_warn "端口 %1 已被占用 (%2)"
    exit /b 1
)
exit /b 0

REM 等待端口可用
:wait_for_port
set /a attempt=1
set /a max_attempts=30
call :log_info "等待 %2 端口 %1 启动..."

:wait_loop
netstat -an | findstr ":%1 " | findstr "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    call :log_info "%2 端口 %1 已启动！"
    exit /b 0
)

echo|set /p="."
timeout /t 2 /nobreak >nul
set /a attempt+=1
if %attempt% leq %max_attempts% goto wait_loop

call :log_error "%2 端口 %1 启动超时"
exit /b 1

REM 停止所有服务
:stop_all_services
call :log_step "停止所有服务..."

REM 停止Java进程
taskkill /f /im java.exe >nul 2>&1
taskkill /f /im javaw.exe >nul 2>&1

REM 停止Node.js进程
taskkill /f /im node.exe >nul 2>&1

REM 停止Nacos
if exist "infrastructure\nacos\nacos\bin\shutdown.cmd" (
    cd infrastructure\nacos\nacos\bin
    call shutdown.cmd >nul 2>&1
    cd ..\..\..\..
)

call :log_info "所有服务已停止"
goto :eof

REM 环境检查
:check_environment
call :log_step "检查运行环境..."

REM 设置Java环境
if exist "%JAVA_HOME%\bin\java.exe" (
    set "PATH=%JAVA_HOME%\bin;%PATH%"
    call :log_info "使用Java路径: %JAVA_HOME%"
) else (
    call :log_error "Java未找到，请检查JAVA_HOME路径: %JAVA_HOME%"
    exit /b 1
)

REM 检查Java版本
call :check_command java
if %errorlevel% neq 0 exit /b 1

for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
    set java_version=%%i
    set java_version=!java_version:"=!
)
call :log_info "Java版本: !java_version!"

REM 检查Maven
call :check_command mvn
if %errorlevel% neq 0 (
    call :log_error "请先安装Maven并添加到PATH环境变量"
    exit /b 1
)
for /f "tokens=*" %%i in ('mvn -version 2^>^&1 ^| findstr "Apache Maven"') do (
    call :log_info "Maven版本: %%i"
)

REM 检查Node.js
call :check_command node
if %errorlevel% neq 0 (
    call :log_error "请先安装Node.js"
    exit /b 1
)
for /f "tokens=*" %%i in ('node -v') do (
    call :log_info "Node.js版本: %%i"
)

REM 检查npm
call :check_command npm
if %errorlevel% neq 0 (
    call :log_error "请先安装npm"
    exit /b 1
)
for /f "tokens=*" %%i in ('npm -v') do (
    call :log_info "npm版本: %%i"
)

call :log_info "MySQL配置: %MYSQL_HOST%:%MYSQL_PORT% (用户: %MYSQL_USER%)"
goto :eof

REM 编译项目
:compile_project
call :log_step "编译项目..."

if not exist "pom.xml" (
    call :log_error "未找到pom.xml文件，请确保在项目根目录运行此脚本"
    exit /b 1
)

call :log_info "清理并编译项目..."
call mvn clean compile -DskipTests=true
if %errorlevel% neq 0 (
    call :log_error "项目编译失败"
    exit /b 1
)

call :log_info "项目编译成功"
goto :eof

REM 安装前端依赖
:install_frontend_deps
call :log_step "安装前端依赖..."

if not exist "frontend" (
    call :log_error "未找到frontend目录"
    exit /b 1
)

cd frontend

if not exist "package.json" (
    call :log_error "未找到package.json文件"
    cd ..
    exit /b 1
)

call :log_info "安装前端依赖..."
call npm install
if %errorlevel% neq 0 (
    call :log_error "前端依赖安装失败"
    cd ..
    exit /b 1
)

cd ..
call :log_info "前端依赖安装成功"
goto :eof

REM 启动Nacos
:start_nacos
call :log_step "启动Nacos注册中心..."

call :check_port 8848 "Nacos"
if %errorlevel% neq 0 (
    call :log_info "Nacos已在运行"
    goto :eof
)

if not exist "infrastructure\nacos\nacos\bin\startup.cmd" (
    call :log_error "未找到Nacos启动脚本"
    exit /b 1
)

cd infrastructure\nacos\nacos\bin

REM 设置JVM参数
set "JAVA_OPT=-server -Xms512m -Xmx512m -Xmn256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m"

REM 启动Nacos
start /b cmd /c "startup.cmd -m standalone >nul 2>&1"

cd ..\..\..\..

REM 等待Nacos启动
call :wait_for_port 8848 "Nacos"

call :log_info "Nacos管理界面: http://localhost:8848/nacos (用户名/密码: nacos/nacos)"
goto :eof

REM 启动微服务
:start_microservice
set service_name=%1
set port=%2

call :log_step "启动 %service_name%..."

call :check_port %port% "%service_name%"
if %errorlevel% neq 0 (
    call :log_info "%service_name% 已在运行"
    goto :eof
)

if not exist "%service_name%" (
    call :log_error "未找到 %service_name% 目录"
    exit /b 1
)

cd %service_name%

REM 后台启动服务
start /b cmd /c "mvn spring-boot:run >../%service_name%.log 2>&1"

cd ..

REM 等待服务启动
timeout /t 5 /nobreak >nul
call :wait_for_port %port% "%service_name%"

call :log_info "%service_name% 启动成功，端口: %port%"
goto :eof

REM 启动前端
:start_frontend
call :log_step "启动前端服务..."

call :check_port 8086 "Frontend"
if %errorlevel% neq 0 (
    call :log_info "前端服务已在运行"
    goto :eof
)

cd frontend

REM 后台启动前端
start /b cmd /c "npm run serve >../frontend.log 2>&1"

cd ..

REM 等待前端启动
timeout /t 10 /nobreak >nul
call :wait_for_port 8086 "Frontend"

call :log_info "前端服务启动成功"
call :log_info "前端访问地址: http://localhost:8086"
goto :eof

REM 显示服务状态
:show_service_status
call :log_step "服务状态检查..."

echo.
echo === 服务状态 ===

set services=Nacos:8848 User-Service:8081 Course-Service:8082 Learning-Service:8083 Recommendation-Service:8084 Discussion-Service:8085 Gateway-Service:8090 Frontend:8086

for %%s in (%services%) do (
    for /f "tokens=1,2 delims=:" %%a in ("%%s") do (
        netstat -an | findstr ":%%b " | findstr "LISTENING" >nul 2>&1
        if !errorlevel! equ 0 (
            echo %GREEN%✓%NC% %%a ^(端口 %%b^) - 运行中
        ) else (
            echo %RED%✗%NC% %%a ^(端口 %%b^) - 未运行
        )
    )
)

echo.
echo === 访问地址 ===
echo • 前端应用: http://localhost:8086
echo • API网关: http://localhost:8090
echo • Nacos控制台: http://localhost:8848/nacos (nacos/nacos)
echo.
echo === 日志文件 ===
echo • 各服务日志: .\{service-name}.log
echo • 前端日志: .\frontend.log
goto :eof

REM 主函数
:main
echo ==========================================
echo   SpringCloud微服务学习平台启动脚本
echo ==========================================
echo.

REM 处理命令行参数
if "%1"=="stop" (
    call :stop_all_services
    goto :end
)
if "%1"=="status" (
    call :show_service_status
    goto :end
)
if "%1"=="restart" (
    call :stop_all_services
    timeout /t 3 /nobreak >nul
)

REM 环境检查
call :check_environment
if %errorlevel% neq 0 goto :end

REM 编译项目
call :compile_project
if %errorlevel% neq 0 goto :end

REM 安装前端依赖
call :install_frontend_deps
if %errorlevel% neq 0 goto :end

REM 启动服务
call :start_nacos
if %errorlevel% neq 0 goto :end

REM 等待Nacos完全启动
timeout /t 5 /nobreak >nul

REM 按顺序启动微服务
call :start_microservice "user-service" 8081
call :start_microservice "course-service" 8082
call :start_microservice "learning-service" 8083
call :start_microservice "recommendation-service" 8084
call :start_microservice "discussion-service" 8085
call :start_microservice "payment-service" 8087
call :start_microservice "gateway-service" 8090

REM 启动前端
call :start_frontend

REM 显示服务状态
call :show_service_status

call :log_info "所有服务启动完成！"
call :log_info "按 Ctrl+C 可以退出，或运行 '%~nx0 stop' 停止所有服务"

:end
pause
