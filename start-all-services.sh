#!/bin/bash

# SpringCloud微服务学习平台一键启动脚本 (macOS版本)
# 作者: AI Assistant
# 日期: 2025-08-01

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        return 1
    fi
    return 0
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口 $port 已被占用 ($service_name)"
        return 1
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    log_info "等待 $service_name 启动..."

    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            log_info "$service_name 启动成功！"
            return 0
        fi

        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    log_error "$service_name 启动超时！"
    return 1
}

# 等待端口可用
wait_for_port() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    log_info "等待 $service_name 端口 $port 启动..."

    while [ $attempt -le $max_attempts ]; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_info "$service_name 端口 $port 已启动！"
            return 0
        fi

        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    log_error "$service_name 端口 $port 启动超时"
    return 1
}

# 停止所有服务
stop_all_services() {
    log_step "停止所有服务..."
    
    # 停止前端服务
    pkill -f "vue-cli-service serve" 2>/dev/null || true
    
    # 停止Spring Boot服务
    pkill -f "spring-boot:run" 2>/dev/null || true
    pkill -f "gateway-service" 2>/dev/null || true
    pkill -f "user-service" 2>/dev/null || true
    pkill -f "course-service" 2>/dev/null || true
    pkill -f "learning-service" 2>/dev/null || true
    pkill -f "recommendation-service" 2>/dev/null || true
    pkill -f "discussion-service" 2>/dev/null || true
    pkill -f "payment-service" 2>/dev/null || true
    
    # 停止Nacos
    if [ -f "infrastructure/nacos/nacos/bin/shutdown.sh" ]; then
        cd infrastructure/nacos/nacos/bin && ./shutdown.sh 2>/dev/null || true
        cd - > /dev/null
    fi
    
    log_info "所有服务已停止"
}

# 环境检查
check_environment() {
    log_step "检查运行环境..."

    # 设置Java环境（使用jenv指定的版本）
    if command -v jenv &> /dev/null; then
        log_info "检测到jenv，设置Java版本为zulu64-22.0.2..."
        export JAVA_HOME=$(jenv javahome zulu64-22.0.2 2>/dev/null || jenv javahome)
        export PATH="$JAVA_HOME/bin:$PATH"
    fi

    # 检查Java
    if ! check_command java; then
        log_error "请先安装Java 17或更高版本"
        exit 1
    fi

    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$java_version" -lt 17 ]; then
        log_error "Java版本过低，需要Java 17或更高版本，当前版本: $java_version"
        exit 1
    fi
    log_info "Java版本检查通过: $(java -version 2>&1 | head -n 1)"

    # 检查Maven
    if ! check_command mvn; then
        log_error "请先安装Maven"
        exit 1
    fi
    log_info "Maven版本: $(mvn -version | head -n 1)"

    # 检查Node.js
    if ! check_command node; then
        log_error "请先安装Node.js"
        exit 1
    fi
    log_info "Node.js版本: $(node -v)"

    # 检查npm
    if ! check_command npm; then
        log_error "请先安装npm"
        exit 1
    fi
    log_info "npm版本: $(npm -v)"

    # 检查MySQL (使用指定路径)
    MYSQL_PATH="/usr/local/mysql-8.4.6-macos15-arm64/bin/mysql"
    if [ -f "$MYSQL_PATH" ]; then
        export PATH="/usr/local/mysql-8.4.6-macos15-arm64/bin:$PATH"
        log_info "MySQL已安装: $($MYSQL_PATH --version)"
    elif check_command mysql; then
        log_info "MySQL已安装: $(mysql --version)"
    else
        log_warn "MySQL未安装，将使用H2内存数据库"
        log_warn "如需使用MySQL，请安装MySQL并创建数据库: learning_platform"
    fi
}

# 编译项目
compile_project() {
    log_step "编译项目..."
    
    if [ ! -f "pom.xml" ]; then
        log_error "未找到pom.xml文件，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    log_info "清理并编译项目..."
    mvn clean compile -DskipTests=true
    
    if [ $? -ne 0 ]; then
        log_error "项目编译失败"
        exit 1
    fi
    
    log_info "项目编译成功"
}

# 安装前端依赖
install_frontend_deps() {
    log_step "安装前端依赖..."
    
    if [ ! -d "frontend" ]; then
        log_error "未找到frontend目录"
        exit 1
    fi
    
    cd frontend
    
    if [ ! -f "package.json" ]; then
        log_error "未找到package.json文件"
        exit 1
    fi
    
    log_info "安装前端依赖..."
    npm install
    
    if [ $? -ne 0 ]; then
        log_error "前端依赖安装失败"
        exit 1
    fi
    
    cd ..
    log_info "前端依赖安装成功"
}

# 检查Nacos是否完整安装
check_nacos_installation() {
    local nacos_jar="infrastructure/nacos/nacos/target/nacos-server.jar"
    local nacos_bin="infrastructure/nacos/nacos/bin"
    local nacos_startup="infrastructure/nacos/nacos/bin/startup.sh"

    if [ ! -f "${nacos_jar}" ]; then
        log_warn "Nacos安装不完整，缺少 nacos-server.jar 文件"
        return 1
    fi

    if [ ! -d "${nacos_bin}" ]; then
        log_warn "Nacos安装不完整，缺少 bin 目录"
        return 1
    fi

    if [ ! -f "${nacos_startup}" ]; then
        log_warn "Nacos安装不完整，缺少启动脚本"
        return 1
    fi

    return 0
}

# 下载并安装Nacos
download_and_install_nacos() {
    local nacos_version="2.3.0"
    local nacos_url="https://github.com/alibaba/nacos/releases/download/${nacos_version}/nacos-server-${nacos_version}.tar.gz"
    local nacos_dir="infrastructure/nacos"

    log_step "自动下载和安装Nacos ${nacos_version}..."

    # 备份现有配置（如果存在）
    if [ -f "${nacos_dir}/nacos/conf/application.properties" ]; then
        log_info "备份现有配置文件..."
        cp "${nacos_dir}/nacos/conf/application.properties" "${nacos_dir}/application.properties.backup"
    fi

    cd "${nacos_dir}"

    # 下载Nacos
    log_info "正在下载 nacos-server-${nacos_version}.tar.gz..."
    if command -v curl >/dev/null 2>&1; then
        curl -L -O "${nacos_url}"
    elif command -v wget >/dev/null 2>&1; then
        wget "${nacos_url}"
    else
        log_error "未找到 curl 或 wget 命令，无法下载Nacos"
        exit 1
    fi

    # 解压Nacos
    log_info "正在解压Nacos..."
    if [ -d "nacos" ]; then
        rm -rf "nacos"
    fi
    tar -xzf "nacos-server-${nacos_version}.tar.gz"

    # 恢复配置文件
    if [ -f "application.properties.backup" ]; then
        log_info "恢复配置文件..."
        cp "application.properties.backup" "nacos/conf/application.properties"
        rm -f "application.properties.backup"
    fi

    # 清理下载文件
    rm -f "nacos-server-${nacos_version}.tar.gz"

    cd - > /dev/null

    log_info "Nacos ${nacos_version} 安装完成"
}

# 启动Nacos
start_nacos() {
    log_step "启动Nacos注册中心..."

    if ! check_port 8848 "Nacos"; then
        log_info "Nacos已在运行"
        return 0
    fi

    # 检查Nacos安装
    if ! check_nacos_installation; then
        download_and_install_nacos
    fi

    if [ ! -f "infrastructure/nacos/nacos/bin/startup.sh" ]; then
        log_error "Nacos安装失败，未找到启动脚本"
        exit 1
    fi

    cd infrastructure/nacos/nacos/bin

    # 设置JVM参数适配macOS
    export JAVA_OPT="-server -Xms512m -Xmx512m -Xmn256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m"

    # 启动Nacos
    ./startup.sh -m standalone > /dev/null 2>&1 &

    cd - > /dev/null

    # 等待Nacos启动
    wait_for_service "http://localhost:8848/nacos" "Nacos"

    log_info "Nacos管理界面: http://localhost:8848/nacos (用户名/密码: nacos/nacos)"
}

# 启动微服务
start_microservice() {
    local service_name=$1
    local port=$2
    
    log_step "启动 $service_name..."
    
    if ! check_port $port "$service_name"; then
        log_info "$service_name 已在运行"
        return 0
    fi
    
    if [ ! -d "$service_name" ]; then
        log_error "未找到 $service_name 目录"
        return 1
    fi
    
    cd $service_name
    
    # 后台启动服务
    nohup mvn spring-boot:run > ../$service_name.log 2>&1 &
    local pid=$!
    
    cd ..
    
    # 等待服务启动
    sleep 5
    wait_for_port "$port" "$service_name"
    
    log_info "$service_name 启动成功，PID: $pid，端口: $port"
}

# 启动前端
start_frontend() {
    log_step "启动前端服务..."
    
    if ! check_port 8086 "Frontend"; then
        log_info "前端服务已在运行"
        return 0
    fi
    
    cd frontend
    
    # 后台启动前端
    nohup npm run serve > ../frontend.log 2>&1 &
    local pid=$!
    
    cd ..
    
    # 等待前端启动
    sleep 10
    wait_for_service "http://localhost:8086" "Frontend"
    
    log_info "前端服务启动成功，PID: $pid"
    log_info "前端访问地址: http://localhost:8086"
}

# 显示服务状态
show_service_status() {
    log_step "服务状态检查..."
    
    echo ""
    echo "=== 服务状态 ==="
    
    services=(
        "Nacos:8848"
        "User-Service:8081"
        "Course-Service:8082"
        "Learning-Service:8083"
        "Recommendation-Service:8084"
        "Discussion-Service:8085"
        "Gateway-Service:8090"
        "Frontend:8086"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} $name (端口 $port) - 运行中"
        else
            echo -e "${RED}✗${NC} $name (端口 $port) - 未运行"
        fi
    done
    
    echo ""
    echo "=== 访问地址 ==="
    echo "• 前端应用: http://localhost:8086"
    echo "• API网关: http://localhost:8090"
    echo "• Nacos控制台: http://localhost:8848/nacos (nacos/nacos)"
    echo ""
    echo "=== 日志文件 ==="
    echo "• 各服务日志: ./{service-name}.log"
    echo "• 前端日志: ./frontend.log"
}

# 主函数
main() {
    echo "=========================================="
    echo "  SpringCloud微服务学习平台启动脚本"
    echo "=========================================="
    echo ""
    
    # 处理命令行参数
    case "${1:-start}" in
        "stop")
            stop_all_services
            exit 0
            ;;
        "status")
            show_service_status
            exit 0
            ;;
        "restart")
            stop_all_services
            sleep 3
            ;;
        "start"|"")
            ;;
        *)
            echo "用法: $0 [start|stop|restart|status]"
            echo "  start   - 启动所有服务 (默认)"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  status  - 查看服务状态"
            exit 1
            ;;
    esac
    
    # 环境检查
    check_environment
    
    # 编译项目
    compile_project
    
    # 安装前端依赖
    install_frontend_deps
    
    # 启动服务
    start_nacos
    
    # 等待Nacos完全启动
    sleep 5
    
    # 按顺序启动微服务
    start_microservice "user-service" 8081
    start_microservice "course-service" 8082
    start_microservice "learning-service" 8083
    start_microservice "recommendation-service" 8084
    start_microservice "discussion-service" 8085
    start_microservice "payment-service" 8087
    start_microservice "gateway-service" 8090
    
    # 启动前端
    start_frontend
    
    # 显示服务状态
    show_service_status
    
    log_info "所有服务启动完成！"
    log_info "按 Ctrl+C 可以查看实时日志，或运行 '$0 stop' 停止所有服务"
}

# 捕获Ctrl+C信号
trap 'echo -e "\n${YELLOW}提示: 服务仍在后台运行，使用 $0 stop 停止所有服务${NC}"; exit 0' INT

# 运行主函数
main "$@"
