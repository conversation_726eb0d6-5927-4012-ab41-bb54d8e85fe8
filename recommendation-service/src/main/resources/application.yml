server:
  port: 8084

spring:
  application:
    name: recommendation-service
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************
    username: study250801
    password: '@yw@%K!@3^Dm'
    
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  level:
    com.learningplatform.recommendation: DEBUG