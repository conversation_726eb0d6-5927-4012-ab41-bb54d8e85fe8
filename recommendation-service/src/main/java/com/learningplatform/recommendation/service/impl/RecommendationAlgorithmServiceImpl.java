package com.learningplatform.recommendation.service.impl;

import com.learningplatform.recommendation.algorithm.AdvancedCollaborativeFiltering;
import com.learningplatform.recommendation.algorithm.AdvancedContentBasedFiltering;
import com.learningplatform.recommendation.model.RecommendationModel;
import com.learningplatform.recommendation.model.UserBehaviorModel;
import com.learningplatform.recommendation.repository.UserBehaviorRepository;
import com.learningplatform.recommendation.service.RecommendationAlgorithmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐算法服务实现类
 */
@Service
public class RecommendationAlgorithmServiceImpl implements RecommendationAlgorithmService {
    
    @Autowired
    private UserBehaviorRepository userBehaviorRepository;
    
    private final AdvancedCollaborativeFiltering collaborativeFiltering;
    private final AdvancedContentBasedFiltering contentBasedFiltering;
    
    // 行为权重配置
    private static final Map<String, Double> BEHAVIOR_WEIGHTS = Map.of(
        "VIEW", 0.1,
        "ENROLL", 0.5,
        "COMPLETE", 1.0,
        "RATE", 0.8,
        "FEEDBACK", 0.3
    );
    
    public RecommendationAlgorithmServiceImpl() {
        this.collaborativeFiltering = new AdvancedCollaborativeFiltering();
        this.contentBasedFiltering = new AdvancedContentBasedFiltering();
    }
    
    @Override
    public List<RecommendationModel> collaborativeFiltering(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit) {
        // 获取所有用户行为数据
        List<UserBehaviorModel> allBehaviors = userBehaviorRepository.findAll();
        
        // 使用高级协同过滤算法
        List<AdvancedCollaborativeFiltering.CourseRecommendation> recommendations = 
            collaborativeFiltering.hybridCollaborativeFiltering(userId, allBehaviors, limit);
        
        // 转换为RecommendationModel
        return recommendations.stream()
            .map(rec -> new RecommendationModel(
                userId,
                rec.courseId,
                BigDecimal.valueOf(rec.score).setScale(3, RoundingMode.HALF_UP),
                rec.reason
            ))
            .collect(Collectors.toList());
    }
    
    @Override
    public List<RecommendationModel> contentBasedFiltering(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit) {
        // 获取所有用户行为数据
        List<UserBehaviorModel> allBehaviors = userBehaviorRepository.findAll();
        
        // 使用高级内容过滤算法（结合TF-IDF和用户偏好建模）
        List<AdvancedContentBasedFiltering.CourseRecommendation> contentRecs = 
            contentBasedFiltering.contentBasedRecommendation(userId, allBehaviors, limit / 2);
        
        List<AdvancedContentBasedFiltering.CourseRecommendation> tfidfRecs = 
            contentBasedFiltering.tfidfBasedRecommendation(userId, allBehaviors, limit / 2);
        
        // 合并两种内容过滤方法的结果
        Map<Long, Double> combinedScores = new HashMap<>();
        
        // 内容过滤权重 0.6
        for (AdvancedContentBasedFiltering.CourseRecommendation rec : contentRecs) {
            combinedScores.merge(rec.courseId, rec.score * 0.6, Double::sum);
        }
        
        // TF-IDF权重 0.4
        for (AdvancedContentBasedFiltering.CourseRecommendation rec : tfidfRecs) {
            combinedScores.merge(rec.courseId, rec.score * 0.4, Double::sum);
        }
        
        // 转换为RecommendationModel并排序
        return combinedScores.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(limit)
            .map(entry -> new RecommendationModel(
                userId,
                entry.getKey(),
                BigDecimal.valueOf(entry.getValue()).setScale(3, RoundingMode.HALF_UP),
                "基于高级内容过滤推荐"
            ))
            .collect(Collectors.toList());
    }
    
    @Override
    public List<RecommendationModel> hybridRecommendation(Long userId, List<UserBehaviorModel> userBehaviors, Integer limit) {
        // 获取所有用户行为数据
        List<UserBehaviorModel> allBehaviors = userBehaviorRepository.findAll();

        // 如果高级算法失败，使用简化的推荐逻辑
        try {
            // 获取协同过滤推荐结果（使用混合协同过滤）
            List<AdvancedCollaborativeFiltering.CourseRecommendation> collaborativeResults =
                collaborativeFiltering.hybridCollaborativeFiltering(userId, allBehaviors, limit);

            // 获取内容过滤推荐结果
            List<AdvancedContentBasedFiltering.CourseRecommendation> contentResults =
                contentBasedFiltering.contentBasedRecommendation(userId, allBehaviors, limit);

            // 获取TF-IDF推荐结果
            List<AdvancedContentBasedFiltering.CourseRecommendation> tfidfResults =
                contentBasedFiltering.tfidfBasedRecommendation(userId, allBehaviors, limit);

            // 合并多种算法的结果
            Map<Long, Double> hybridScores = new HashMap<>();

            // 协同过滤权重 0.5
            for (AdvancedCollaborativeFiltering.CourseRecommendation rec : collaborativeResults) {
                hybridScores.merge(rec.courseId, rec.score * 0.5, Double::sum);
            }

            // 内容过滤权重 0.3
            for (AdvancedContentBasedFiltering.CourseRecommendation rec : contentResults) {
                hybridScores.merge(rec.courseId, rec.score * 0.3, Double::sum);
            }

            // TF-IDF权重 0.2
            for (AdvancedContentBasedFiltering.CourseRecommendation rec : tfidfResults) {
                hybridScores.merge(rec.courseId, rec.score * 0.2, Double::sum);
            }

            // 如果高级算法都没有结果，使用简化推荐
            if (hybridScores.isEmpty()) {
                return getSimplifiedRecommendations(userId, userBehaviors, allBehaviors, limit);
            }

            // 生成最终推荐结果
            return hybridScores.entrySet().stream()
                .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
                .limit(limit)
                .map(entry -> new RecommendationModel(
                    userId,
                    entry.getKey(),
                    BigDecimal.valueOf(entry.getValue()).setScale(3, RoundingMode.HALF_UP),
                    "基于混合推荐算法"
                ))
                .collect(Collectors.toList());

        } catch (Exception e) {
            // 高级算法异常时使用简化推荐
            return getSimplifiedRecommendations(userId, userBehaviors, allBehaviors, limit);
        }
    }
    
    @Override
    public List<RecommendationModel> popularCourseRecommendation(Long userId, Integer limit) {
        List<UserBehaviorModel> allBehaviors = userBehaviorRepository.findAll();
        
        if (allBehaviors.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 统计课程热门度（加权）
        Map<Long, Double> coursePopularity = new HashMap<>();
        
        for (UserBehaviorModel behavior : allBehaviors) {
            Double weight = BEHAVIOR_WEIGHTS.getOrDefault(behavior.getBehaviorType(), 0.1);
            Double behaviorValue = behavior.getBehaviorValue() != null ? behavior.getBehaviorValue().doubleValue() : 1.0;
            
            coursePopularity.merge(behavior.getCourseId(), weight * behaviorValue, Double::sum);
        }
        
        // 生成热门课程推荐
        return coursePopularity.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(limit)
            .map(entry -> new RecommendationModel(
                userId, 
                entry.getKey(), 
                BigDecimal.valueOf(entry.getValue() / 10.0).setScale(2, RoundingMode.HALF_UP), // 归一化分数
                "热门课程推荐"
            ))
            .collect(Collectors.toList());
    }

    /**
     * 简化推荐算法 - 当高级算法失败时使用
     */
    private List<RecommendationModel> getSimplifiedRecommendations(Long userId, List<UserBehaviorModel> userBehaviors, List<UserBehaviorModel> allBehaviors, Integer limit) {
        // 获取用户已学习的课程
        Set<Long> userCourses = userBehaviors.stream()
            .map(UserBehaviorModel::getCourseId)
            .collect(Collectors.toSet());

        // 统计课程热门度（基于所有用户行为）
        Map<Long, Double> coursePopularity = new HashMap<>();

        for (UserBehaviorModel behavior : allBehaviors) {
            Double weight = BEHAVIOR_WEIGHTS.getOrDefault(behavior.getBehaviorType(), 0.1);
            Double behaviorValue = behavior.getBehaviorValue() != null ? behavior.getBehaviorValue().doubleValue() : 1.0;
            coursePopularity.merge(behavior.getCourseId(), weight * behaviorValue, Double::sum);
        }

        // 推荐用户没有学过的热门课程
        return coursePopularity.entrySet().stream()
            .filter(entry -> !userCourses.contains(entry.getKey()))
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(limit)
            .map(entry -> new RecommendationModel(
                userId,
                entry.getKey(),
                BigDecimal.valueOf(entry.getValue()).setScale(3, RoundingMode.HALF_UP),
                "基于热门度推荐"
            ))
            .collect(Collectors.toList());
    }

}