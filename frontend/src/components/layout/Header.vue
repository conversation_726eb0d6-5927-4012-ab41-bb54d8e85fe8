<template>
  <header class="header">
    <div class="container">
      <div class="header-content">
        <!-- Logo -->
        <div class="logo">
          <router-link to="/" class="logo-link">
            <h1>学习平台</h1>
          </router-link>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav">
          <ul class="nav-list">
            <li class="nav-item">
              <router-link to="/" class="nav-link">首页</router-link>
            </li>
            <li class="nav-item">
              <router-link to="/courses" class="nav-link">课程</router-link>
            </li>
            <li class="nav-item" v-if="isAuthenticated">
              <router-link to="/learning" class="nav-link">学习</router-link>
            </li>
            <li class="nav-item" v-if="isAuthenticated">
              <router-link to="/recommendations" class="nav-link">推荐</router-link>
            </li>
            <li class="nav-item" v-if="isAuthenticated">
              <router-link to="/course/manage" class="nav-link">我的课程</router-link>
            </li>
          </ul>
        </nav>
        
        <!-- 用户菜单 -->
        <div class="user-menu">
          <div v-if="!isAuthenticated" class="auth-buttons">
            <router-link to="/login" class="btn btn-outline">登录</router-link>
            <router-link to="/register" class="btn btn-primary">注册</router-link>
          </div>
          <div v-else class="user-info">
            <div class="dropdown">
              <button class="dropdown-toggle" @click="toggleDropdown">
                <span class="user-name">{{ displayName }}</span>
                <span class="dropdown-arrow">▼</span>
              </button>
              <div class="dropdown-menu" v-show="showDropdown">
                <router-link to="/profile" class="dropdown-item" @click="closeDropdown">
                  个人资料
                </router-link>
                <button class="dropdown-item" @click="handleLogout">
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Header',
  setup() {
    const store = useStore()
    const router = useRouter()
    const showDropdown = ref(false)
    
    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    const user = computed(() => store.getters['auth/user'])
    const userRole = computed(() => localStorage.getItem('userRole'))

    // 计算显示名称：优先显示nickname，没有则显示username
    const displayName = computed(() => {
      if (!user.value) return '用户'
      return user.value.nickname || user.value.username || '用户'
    })
    
    const toggleDropdown = () => {
      showDropdown.value = !showDropdown.value
    }
    
    const closeDropdown = () => {
      showDropdown.value = false
    }
    
    const handleLogout = () => {
      store.dispatch('auth/logout')
      router.push('/')
      closeDropdown()
    }
    
    return {
      isAuthenticated,
      user,
      userRole,
      displayName,
      showDropdown,
      toggleDropdown,
      closeDropdown,
      handleLogout
    }
  }
}
</script>

<style scoped>
.header {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.logo-link {
  text-decoration: none;
  color: #333;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  color: #007bff;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 30px;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #007bff;
}

.auth-buttons {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.btn-outline {
  color: #007bff;
  border-color: #007bff;
}

.btn-outline:hover {
  background-color: #007bff;
  color: white;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.dropdown {
  position: relative;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px;
}

.user-name {
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-width: 120px;
  z-index: 1001;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 10px 15px;
  text-decoration: none;
  color: #333;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}
</style>