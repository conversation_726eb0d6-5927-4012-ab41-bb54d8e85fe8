<template>
  <div class="learning-statistics">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>学习统计</h1>
        <p>详细的学习数据分析和统计信息</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>正在加载统计数据...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error">
        <p>{{ error }}</p>
        <button @click="loadStatistics" class="retry-btn">重试</button>
      </div>

      <!-- 统计内容 -->
      <div v-else class="statistics-content">
        <!-- 总体统计 -->
        <div class="overview-stats">
          <div class="stat-card">
            <div class="stat-icon">📚</div>
            <div class="stat-info">
              <h3>{{ statistics.totalEnrolledCourses || 0 }}</h3>
              <p>已注册课程</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-info">
              <h3>{{ statistics.completedCourses || 0 }}</h3>
              <p>已完成课程</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📖</div>
            <div class="stat-info">
              <h3>{{ statistics.completedChapters || 0 }}</h3>
              <p>已完成章节</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📚</div>
            <div class="stat-info">
              <h3>{{ statistics.inProgressCourses || 0 }}</h3>
              <p>正在学习</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⏰</div>
            <div class="stat-info">
              <h3>{{ formatLearningHours(statistics.totalLearningHours) }}</h3>
              <p>学习时长</p>
            </div>
          </div>
        </div>

        <!-- 学习进度详情 -->
        <div class="progress-details">
          <h2>课程学习进度</h2>
          <div v-if="statistics.courseProgress && statistics.courseProgress.length > 0" class="course-progress-list">
            <div v-for="course in statistics.courseProgress" :key="course.courseId" class="course-progress-item">
              <div class="course-info">
                <img :src="course.coverImage || '/default-course.png'" :alt="course.title" class="course-image" />
                <div class="course-details">
                  <h3>{{ course.title }}</h3>
                  <p>{{ course.description }}</p>
                  <div class="course-meta">
                    <span>讲师：{{ course.teacherName }}</span>
                    <span>难度：{{ course.difficultyLevel }}</span>
                  </div>
                </div>
              </div>
              <div class="progress-info">
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: course.progressPercentage + '%' }"></div>
                </div>
                <div class="progress-text">
                  <span>{{ course.progressPercentage.toFixed(1) }}%</span>
                  <span>{{ course.completedChapters }}/{{ course.totalChapters }} 章节</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-state">
            <p>暂无课程学习记录</p>
            <router-link to="/courses" class="browse-courses-btn">浏览课程</router-link>
          </div>
        </div>

        <!-- 学习活动时间线 -->
        <div class="activity-timeline">
          <h2>最近学习活动</h2>
          <div v-if="statistics.recentActivities && statistics.recentActivities.length > 0" class="timeline-list">
            <div v-for="activity in statistics.recentActivities" :key="activity.id" class="timeline-item">
              <div class="timeline-dot"></div>
              <div class="timeline-content">
                <h4>{{ activity.title }}</h4>
                <p>{{ activity.description }}</p>
                <span class="timeline-time">{{ formatTime(activity.createdAt) }}</span>
              </div>
            </div>
          </div>
          <div v-else class="empty-state">
            <p>暂无学习活动记录</p>
          </div>
        </div>

        <!-- 学习成就 -->
        <div class="achievements">
          <h2>学习成就</h2>
          <div class="achievement-grid">
            <div class="achievement-item" :class="{ unlocked: statistics.completedCourses >= 1 }">
              <div class="achievement-icon">🎓</div>
              <div class="achievement-info">
                <h4>初学者</h4>
                <p>完成第一门课程</p>
              </div>
            </div>
            <div class="achievement-item" :class="{ unlocked: statistics.completedCourses >= 5 }">
              <div class="achievement-icon">📚</div>
              <div class="achievement-info">
                <h4>学习达人</h4>
                <p>完成5门课程</p>
              </div>
            </div>
            <div class="achievement-item" :class="{ unlocked: statistics.completedChapters >= 50 }">
              <div class="achievement-icon">⭐</div>
              <div class="achievement-info">
                <h4>章节大师</h4>
                <p>完成50个章节</p>
              </div>
            </div>
            <div class="achievement-item" :class="{ unlocked: statistics.totalLearningHours >= 100 }">
              <div class="achievement-icon">🏆</div>
              <div class="achievement-info">
                <h4>时间管理大师</h4>
                <p>累计学习100小时</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import learningApi from '../../api/learning'

export default {
  name: 'LearningStatistics',
  setup() {
    const store = useStore()
    const loading = ref(true)
    const error = ref(null)
    const statistics = ref({})

    const loadStatistics = async () => {
      try {
        loading.value = true
        error.value = null

        // 获取学习仪表板数据
        const dashboardResponse = await learningApi.getDashboard()
        if (dashboardResponse.data.success) {
          statistics.value = dashboardResponse.data.data

          // 获取已注册课程的详细信息
          if (statistics.value.totalEnrolledCourses > 0) {
            try {
              const enrolledCoursesResponse = await learningApi.getEnrolledCourses()
              if (enrolledCoursesResponse.data.success) {
                const enrolledCourses = enrolledCoursesResponse.data.data || []
                statistics.value.courseProgress = enrolledCourses.map(course => ({
                  courseId: course.id,
                  title: course.title || `课程 #${course.id}`,
                  description: course.description || '暂无描述',
                  coverImage: course.coverImage,
                  teacherName: course.teacherName || '未知讲师',
                  difficultyLevel: course.difficultyLevel || 'BEGINNER',
                  progressPercentage: course.progressPercentage || 0,
                  completedChapters: course.completedChapters || 0,
                  totalChapters: course.totalChapters || 0
                }))
              }
            } catch (courseError) {
              console.error('获取课程详情失败:', courseError)
              // 如果获取课程详情失败，使用基础数据
              statistics.value.courseProgress = []
            }
          } else {
            statistics.value.courseProgress = []
          }

          // 生成最近活动数据
          statistics.value.recentActivities = []

          // 如果有最近注册的课程，添加到活动中
          if (statistics.value.recentEnrollments && statistics.value.recentEnrollments.length > 0) {
            statistics.value.recentEnrollments.forEach(enrollment => {
              statistics.value.recentActivities.push({
                id: `enrollment-${enrollment.courseId}`,
                title: '课程报名',
                description: `报名了《${enrollment.courseTitle || '未知课程'}》`,
                createdAt: enrollment.enrolledAt || new Date().toISOString(),
                type: 'enrollment'
              })
            })
          }

          // 如果没有真实活动数据，显示提示信息
          if (statistics.value.recentActivities.length === 0) {
            statistics.value.recentActivities = [{
              id: 'no-activity',
              title: '暂无学习活动',
              description: '开始学习课程后，这里将显示您的学习活动记录',
              createdAt: new Date().toISOString(),
              type: 'info'
            }]
          }
        }
      } catch (err) {
        console.error('加载统计数据失败:', err)
        error.value = err.response?.data?.message || '加载统计数据失败'
      } finally {
        loading.value = false
      }
    }

    const formatLearningHours = (hours) => {
      if (hours < 1) {
        return `${Math.round(hours * 60)}分钟`
      }
      return `${hours.toFixed(1)}小时`
    }

    const formatTime = (timeString) => {
      const date = new Date(timeString)
      const now = new Date()
      const diffMs = now - date
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      
      if (diffDays === 0) {
        return '今天'
      } else if (diffDays === 1) {
        return '昨天'
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString()
      }
    }

    onMounted(() => {
      loadStatistics()
    })

    return {
      loading,
      error,
      statistics,
      loadStatistics,
      formatLearningHours,
      formatTime
    }
  }
}
</script>

<style scoped>
.learning-statistics {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.loading, .error {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 10px;
}

.retry-btn:hover {
  background: #0056b3;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 20px;
}

.stat-icon {
  font-size: 3rem;
  opacity: 0.8;
}

.stat-info h3 {
  font-size: 2rem;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.stat-info p {
  color: #7f8c8d;
  margin: 0;
}

.progress-details, .activity-timeline, .achievements {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.progress-details h2, .activity-timeline h2, .achievements h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.course-progress-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.course-progress-item:last-child {
  border-bottom: none;
}

.course-info {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.course-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.course-details h3 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.course-details p {
  margin: 0 0 10px 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.course-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: #95a5a6;
}

.progress-info {
  min-width: 200px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.timeline-list {
  position: relative;
  padding-left: 30px;
}

.timeline-list::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #ecf0f1;
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
}

.timeline-dot {
  position: absolute;
  left: -25px;
  top: 5px;
  width: 12px;
  height: 12px;
  background: #3498db;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #ecf0f1;
}

.timeline-content h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.timeline-content p {
  margin: 0 0 10px 0;
  color: #7f8c8d;
}

.timeline-time {
  font-size: 0.8rem;
  color: #95a5a6;
}

.achievement-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border: 2px solid #ecf0f1;
  border-radius: 10px;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.achievement-item.unlocked {
  opacity: 1;
  border-color: #3498db;
  background: #f8f9ff;
}

.achievement-icon {
  font-size: 2rem;
}

.achievement-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.achievement-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.browse-courses-btn {
  display: inline-block;
  background: #007bff;
  color: white;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 5px;
  margin-top: 15px;
  transition: background 0.3s ease;
}

.browse-courses-btn:hover {
  background: #0056b3;
}

@media (max-width: 768px) {
  .course-progress-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .progress-info {
    width: 100%;
    min-width: auto;
  }
  
  .achievement-grid {
    grid-template-columns: 1fr;
  }
}
</style>
