import axios from 'axios'
import store from '../store'
import router from '../router'

// 创建统一的API实例，通过网关访问所有微服务
const api = axios.create({
  baseURL: 'http://localhost:8090', // 网关端口，统一入口
  timeout: 10000,
  withCredentials: true, // 允许携带认证信息
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加JWT token到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response) {
      // 处理HTTP错误状态码
      switch (error.response.status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          // 同步更新Vuex状态
          store.dispatch('auth/logout')
          // 如果当前不在登录页，则跳转到登录页
          if (router.currentRoute.value.path !== '/login') {
            router.push('/login')
          }
          break
        case 403:
          // 禁止访问
          console.error('权限不足')
          break
        case 404:
          // 资源不存在
          console.error('请求的资源不存在')
          break
        case 500:
          // 服务器错误
          console.error('服务器内部错误')
          break
        default:
          console.error('请求失败:', error.response.data.message || '未知错误')
      }
    } else if (error.request) {
      // 网络错误
      console.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      console.error('请求配置错误:', error.message)
    }
    return Promise.reject(error)
  }
)

export default api