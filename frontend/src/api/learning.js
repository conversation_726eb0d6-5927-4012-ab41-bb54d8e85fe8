import api from './index'

export default {
  // 获取学习仪表板
  getDashboard() {
    return api.get('/api/learning/dashboard')
  },

  // 获取学习仪表板 (别名)
  getLearningDashboard() {
    return api.get('/api/learning/dashboard')
  },

  // 获取学习进度
  getProgress(courseId) {
    return api.get(`/api/learning/progress/${courseId}`)
  },

  // 更新学习进度
  updateProgress(progressData) {
    return api.post('/api/learning/progress', progressData)
  },

  // 提交测验
  submitQuiz(quizData) {
    return api.post('/api/learning/quiz/submit', quizData)
  },

  // 获取学习统计
  getStatistics() {
    return api.get('/api/learning/statistics')
  },

  // 获取已注册课程
  getEnrolledCourses() {
    return api.get('/api/learning/enrolled-courses')
  },

  // 获取测验记录
  getQuizRecords(courseId) {
    return api.get(`/api/learning/quiz-records/${courseId}`)
  },

  // 检查是否已报名课程
  checkEnrollment(courseId) {
    return api.get('/api/learning/enrolled', { params: { courseId } })
  },

  // 退选课程
  unenrollCourse(courseId) {
    return api.delete(`/api/learning/unenroll/${courseId}`)
  },

  // 完成章节学习
  completeChapter(data) {
    return api.post('/api/learning/progress/complete', data)
  },

  // 获取课程学习进度
  getCourseProgress(courseId) {
    return api.get(`/api/learning/progress/${courseId}`)
  }
}