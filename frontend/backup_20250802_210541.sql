-- MySQL dump 10.13  Distrib 8.4.6, for macos15 (arm64)
--
-- Host: localhost    Database: learning_platform
-- ------------------------------------------------------
-- Server version	8.4.6

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `courses`
--

DROP TABLE IF EXISTS `courses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `courses` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `content` text COLLATE utf8mb4_unicode_ci,
  `instructor_id` bigint DEFAULT NULL,
  `category` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `difficulty_level` enum('BEGINNER','INTERMEDIATE','ADVANCED') COLLATE utf8mb4_unicode_ci DEFAULT 'BEGINNER',
  `duration_hours` int DEFAULT '0',
  `price` decimal(10,2) DEFAULT '0.00',
  `status` enum('DRAFT','PUBLISHED','ARCHIVED') COLLATE utf8mb4_unicode_ci DEFAULT 'PUBLISHED',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_instructor` (`instructor_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courses`
--

LOCK TABLES `courses` WRITE;
/*!40000 ALTER TABLE `courses` DISABLE KEYS */;
INSERT INTO `courses` VALUES (1,'Java基础编程','Java编程语言基础课程，适合初学者','Java语法、面向对象编程、集合框架等基础知识',4,'Programming','BEGINNER',40,199.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(2,'Spring Boot实战','Spring Boot框架实战开发课程','Spring Boot核心概念、自动配置、Web开发、数据访问等',4,'Programming','INTERMEDIATE',60,299.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(3,'MySQL数据库设计','MySQL数据库设计与优化课程','SQL语法、数据库设计、索引优化、性能调优',5,'Database','INTERMEDIATE',35,249.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(4,'前端开发入门','HTML、CSS、JavaScript基础课程','Web前端开发基础知识，包括HTML5、CSS3、ES6等',4,'Frontend','BEGINNER',45,179.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(5,'Vue.js框架开发','Vue.js前端框架开发课程','Vue.js组件开发、路由管理、状态管理、项目实战',5,'Frontend','INTERMEDIATE',50,279.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(6,'Python数据分析','Python数据分析与可视化课程','NumPy、Pandas、Matplotlib、数据清洗与分析',4,'DataScience','INTERMEDIATE',55,329.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(7,'机器学习基础','机器学习算法与应用课程','监督学习、无监督学习、深度学习基础、实际案例',5,'DataScience','ADVANCED',80,499.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(8,'Docker容器技术','Docker容器化部署课程','Docker基础、镜像制作、容器编排、微服务部署',4,'DevOps','INTERMEDIATE',30,219.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(9,'Kubernetes集群管理','Kubernetes容器编排课程','K8s集群搭建、Pod管理、服务发现、自动扩缩容',5,'DevOps','ADVANCED',65,399.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(10,'React开发实战','React前端框架开发课程','React组件、Hooks、状态管理、路由、项目实战',4,'Frontend','INTERMEDIATE',55,299.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(11,'Node.js后端开发','Node.js服务端开发课程','Express框架、API开发、数据库集成、身份认证',5,'Backend','INTERMEDIATE',45,259.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(12,'微服务架构设计','微服务架构设计与实现课程','服务拆分、API网关、服务发现、分布式事务',4,'Architecture','ADVANCED',70,449.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(13,'Redis缓存技术','Redis缓存数据库课程','Redis数据结构、持久化、集群、缓存策略',5,'Database','INTERMEDIATE',25,189.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(14,'Git版本控制','Git版本控制系统课程','Git基础命令、分支管理、协作开发、工作流程',4,'Tools','BEGINNER',20,99.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(15,'Linux系统管理','Linux系统管理与运维课程','Linux基础、文件系统、进程管理、网络配置',5,'System','INTERMEDIATE',40,229.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(16,'算法与数据结构','算法与数据结构基础课程','排序算法、查找算法、树、图、动态规划',4,'Algorithm','INTERMEDIATE',60,349.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(17,'网络安全基础','网络安全与信息安全课程','网络攻防、加密技术、安全协议、漏洞分析',5,'Security','INTERMEDIATE',45,299.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(18,'UI/UX设计','用户界面与用户体验设计课程','设计原则、原型设计、用户研究、交互设计',4,'Design','BEGINNER',35,199.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(19,'移动应用开发','Android/iOS移动应用开发课程','移动端开发、跨平台框架、应用发布',5,'Mobile','INTERMEDIATE',65,379.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(20,'区块链技术','区块链技术原理与应用课程','区块链基础、智能合约、DApp开发、加密货币',4,'Blockchain','ADVANCED',50,399.00,'PUBLISHED','2025-08-01 18:30:29','2025-08-01 18:30:29',0);
/*!40000 ALTER TABLE `courses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `learning_records`
--

DROP TABLE IF EXISTS `learning_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `learning_records` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `course_id` bigint NOT NULL,
  `progress` decimal(5,2) DEFAULT '0.00',
  `status` enum('NOT_STARTED','IN_PROGRESS','COMPLETED','PAUSED') COLLATE utf8mb4_unicode_ci DEFAULT 'NOT_STARTED',
  `start_time` timestamp NULL DEFAULT NULL,
  `completion_time` timestamp NULL DEFAULT NULL,
  `last_access_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_course` (`user_id`,`course_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `learning_records`
--

LOCK TABLES `learning_records` WRITE;
/*!40000 ALTER TABLE `learning_records` DISABLE KEYS */;
INSERT INTO `learning_records` VALUES (1,1,1,100.00,'COMPLETED','2025-07-01 01:00:00','2025-07-15 10:30:00','2025-07-15 10:30:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(2,1,2,85.50,'IN_PROGRESS','2025-07-16 02:00:00',NULL,'2025-08-01 06:20:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(3,1,3,100.00,'COMPLETED','2025-06-15 00:30:00','2025-06-30 08:45:00','2025-06-30 08:45:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(4,1,8,75.20,'IN_PROGRESS','2025-07-20 03:00:00',NULL,'2025-08-01 01:15:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(5,1,12,60.30,'IN_PROGRESS','2025-07-25 05:30:00',NULL,'2025-07-31 09:00:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(6,2,1,100.00,'COMPLETED','2025-06-20 02:30:00','2025-07-05 11:15:00','2025-07-05 11:15:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(7,2,4,100.00,'COMPLETED','2025-07-06 01:15:00','2025-07-25 12:30:00','2025-07-25 12:30:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(8,2,5,90.75,'IN_PROGRESS','2025-07-26 06:00:00',NULL,'2025-08-01 08:45:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(9,2,10,45.60,'IN_PROGRESS','2025-07-28 03:30:00',NULL,'2025-08-01 05:20:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(10,2,14,100.00,'COMPLETED','2025-06-01 00:00:00','2025-06-10 09:30:00','2025-06-10 09:30:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(11,9,1,95.80,'IN_PROGRESS','2025-07-10 01:45:00',NULL,'2025-08-01 07:30:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(12,9,6,100.00,'COMPLETED','2025-06-25 02:00:00','2025-07-20 10:45:00','2025-07-20 10:45:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(13,9,7,70.25,'IN_PROGRESS','2025-07-21 05:15:00',NULL,'2025-08-01 03:00:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(14,9,16,55.40,'IN_PROGRESS','2025-07-15 08:30:00',NULL,'2025-07-30 06:15:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(15,6,1,100.00,'COMPLETED','2025-06-10 01:00:00','2025-06-25 09:30:00','2025-06-25 09:30:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(16,6,4,80.30,'IN_PROGRESS','2025-06-26 02:15:00',NULL,'2025-07-31 08:20:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(17,6,14,100.00,'COMPLETED','2025-05-15 00:30:00','2025-05-25 11:00:00','2025-05-25 11:00:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(18,7,2,65.75,'IN_PROGRESS','2025-07-05 03:00:00',NULL,'2025-08-01 04:45:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(19,7,3,100.00,'COMPLETED','2025-06-01 01:30:00','2025-06-20 10:15:00','2025-06-20 10:15:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(20,7,15,40.20,'IN_PROGRESS','2025-07-22 06:30:00',NULL,'2025-07-29 02:30:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(21,8,5,100.00,'COMPLETED','2025-06-15 02:45:00','2025-07-10 11:30:00','2025-07-10 11:30:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(22,8,10,85.60,'IN_PROGRESS','2025-07-11 05:00:00',NULL,'2025-08-01 07:45:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(23,8,18,30.15,'IN_PROGRESS','2025-07-25 08:15:00',NULL,'2025-07-28 03:30:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(24,10,6,75.45,'IN_PROGRESS','2025-07-08 01:15:00',NULL,'2025-08-01 06:00:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(25,10,7,50.30,'IN_PROGRESS','2025-07-18 03:45:00',NULL,'2025-07-31 08:30:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(26,11,9,60.80,'IN_PROGRESS','2025-07-12 02:30:00',NULL,'2025-08-01 05:15:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(27,11,12,35.25,'IN_PROGRESS','2025-07-20 07:00:00',NULL,'2025-07-30 01:45:00','2025-08-01 18:30:29','2025-08-01 18:30:29',0);
/*!40000 ALTER TABLE `learning_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `recommendations`
--

DROP TABLE IF EXISTS `recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recommendations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `course_id` bigint NOT NULL,
  `algorithm_type` enum('COLLABORATIVE','CONTENT_BASED','HYBRID','POPULAR') COLLATE utf8mb4_unicode_ci NOT NULL,
  `score` decimal(5,4) NOT NULL,
  `reason` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_algorithm` (`algorithm_type`),
  KEY `idx_score` (`score` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `recommendations`
--

LOCK TABLES `recommendations` WRITE;
/*!40000 ALTER TABLE `recommendations` DISABLE KEYS */;
INSERT INTO `recommendations` VALUES (1,1,4,'COLLABORATIVE',0.8500,'基于相似用户的学习偏好推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(2,1,5,'COLLABORATIVE',0.7800,'学习了Java基础的用户也喜欢前端开发','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(3,1,6,'CONTENT_BASED',0.9200,'基于您对编程课程的兴趣','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(4,1,7,'HYBRID',0.8900,'结合协同过滤和内容分析的推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(5,1,9,'COLLABORATIVE',0.7600,'相似背景用户的热门选择','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(6,1,10,'CONTENT_BASED',0.8300,'与您已学课程内容相关','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(7,1,11,'HYBRID',0.8100,'综合推荐算法建议','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(8,1,13,'COLLABORATIVE',0.7400,'基于用户群体偏好','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(9,1,15,'CONTENT_BASED',0.7900,'技术栈相关推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(10,1,16,'HYBRID',0.8600,'个性化混合推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(11,1,17,'COLLABORATIVE',0.7200,'相似用户喜欢的课程','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(12,1,19,'CONTENT_BASED',0.7700,'基于学习历史的内容推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(13,2,2,'COLLABORATIVE',0.8700,'学习了Java基础的用户推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(14,2,3,'CONTENT_BASED',0.8400,'与前端开发相关的技术','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(15,2,6,'HYBRID',0.9100,'数据分析是热门发展方向','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(16,2,7,'COLLABORATIVE',0.8800,'相似学习路径用户的选择','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(17,2,8,'CONTENT_BASED',0.8000,'技术栈扩展推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(18,2,9,'HYBRID',0.8500,'进阶技术课程推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(19,2,11,'COLLABORATIVE',0.7900,'后端开发进阶课程','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(20,2,12,'CONTENT_BASED',0.8200,'架构设计相关推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(21,2,13,'HYBRID',0.7700,'缓存技术补充学习','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(22,2,16,'COLLABORATIVE',0.8300,'算法基础强化推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(23,2,19,'CONTENT_BASED',0.7500,'移动开发扩展方向','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(24,2,20,'HYBRID',0.7800,'新兴技术探索推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(25,9,2,'COLLABORATIVE',0.8600,'基于Java基础学习的进阶推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(26,9,3,'CONTENT_BASED',0.8200,'数据库技术是编程必备技能','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(27,9,4,'HYBRID',0.7900,'前端技术栈补充','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(28,9,5,'COLLABORATIVE',0.8400,'Vue.js是热门前端框架','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(29,9,8,'CONTENT_BASED',0.8700,'容器技术是现代开发趋势','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(30,9,9,'HYBRID',0.8300,'Kubernetes进阶容器编排','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(31,9,10,'COLLABORATIVE',0.7800,'React与Vue.js相似技术栈','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(32,9,11,'CONTENT_BASED',0.8100,'Node.js后端开发扩展','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(33,9,12,'HYBRID',0.8500,'微服务架构设计进阶','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(34,9,13,'COLLABORATIVE',0.7600,'Redis缓存技术实用推荐','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(35,9,14,'CONTENT_BASED',0.7400,'Git版本控制基础工具','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(36,9,15,'HYBRID',0.7700,'Linux系统管理技能','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(37,9,17,'COLLABORATIVE',0.7500,'网络安全意识培养','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(38,9,18,'CONTENT_BASED',0.7300,'UI/UX设计思维拓展','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(39,9,19,'HYBRID',0.7900,'移动应用开发新方向','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(40,9,20,'COLLABORATIVE',0.8000,'区块链技术前沿探索','2025-08-01 18:30:29','2025-08-01 18:30:29',0);
/*!40000 ALTER TABLE `recommendations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_behaviors`
--

DROP TABLE IF EXISTS `user_behaviors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_behaviors` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `course_id` bigint NOT NULL,
  `behavior_type` enum('VIEW','CLICK','ENROLL','COMPLETE','LIKE','SHARE','COMMENT') COLLATE utf8mb4_unicode_ci NOT NULL,
  `behavior_value` decimal(5,2) DEFAULT '1.00',
  `session_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_behavior_type` (`behavior_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=160 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_behaviors`
--

LOCK TABLES `user_behaviors` WRITE;
/*!40000 ALTER TABLE `user_behaviors` DISABLE KEYS */;
INSERT INTO `user_behaviors` VALUES (1,1,1,'VIEW',1.00,NULL,NULL,NULL,'2025-07-01 01:00:00',0),(2,1,1,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-01 01:05:00',0),(3,1,1,'COMPLETE',5.00,NULL,NULL,NULL,'2025-07-15 10:30:00',0),(4,1,1,'LIKE',2.00,NULL,NULL,NULL,'2025-07-15 10:35:00',0),(5,1,2,'VIEW',1.00,NULL,NULL,NULL,'2025-07-16 02:00:00',0),(6,1,2,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-16 02:05:00',0),(7,1,2,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 06:20:00',0),(8,1,3,'VIEW',1.00,NULL,NULL,NULL,'2025-06-15 00:30:00',0),(9,1,3,'ENROLL',3.00,NULL,NULL,NULL,'2025-06-15 00:35:00',0),(10,1,3,'COMPLETE',5.00,NULL,NULL,NULL,'2025-06-30 08:45:00',0),(11,1,3,'LIKE',2.00,NULL,NULL,NULL,'2025-06-30 08:50:00',0),(12,1,8,'VIEW',1.00,NULL,NULL,NULL,'2025-07-20 03:00:00',0),(13,1,8,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-20 03:05:00',0),(14,1,8,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 01:15:00',0),(15,1,12,'VIEW',1.00,NULL,NULL,NULL,'2025-07-25 05:30:00',0),(16,1,12,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-25 05:35:00',0),(17,1,12,'CLICK',1.00,NULL,NULL,NULL,'2025-07-31 09:00:00',0),(18,1,4,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 02:15:00',0),(19,1,5,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 02:20:00',0),(20,1,6,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 02:25:00',0),(21,1,7,'VIEW',1.00,NULL,NULL,NULL,'2025-07-29 06:30:00',0),(22,1,9,'VIEW',1.00,NULL,NULL,NULL,'2025-07-29 06:35:00',0),(23,1,10,'VIEW',1.00,NULL,NULL,NULL,'2025-07-30 08:40:00',0),(24,1,11,'VIEW',1.00,NULL,NULL,NULL,'2025-07-30 08:45:00',0),(25,2,1,'VIEW',1.00,NULL,NULL,NULL,'2025-06-20 02:30:00',0),(26,2,1,'ENROLL',3.00,NULL,NULL,NULL,'2025-06-20 02:35:00',0),(27,2,1,'COMPLETE',5.00,NULL,NULL,NULL,'2025-07-05 11:15:00',0),(28,2,1,'LIKE',2.00,NULL,NULL,NULL,'2025-07-05 11:20:00',0),(29,2,4,'VIEW',1.00,NULL,NULL,NULL,'2025-07-06 01:15:00',0),(30,2,4,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-06 01:20:00',0),(31,2,4,'COMPLETE',5.00,NULL,NULL,NULL,'2025-07-25 12:30:00',0),(32,2,4,'LIKE',2.00,NULL,NULL,NULL,'2025-07-25 12:35:00',0),(33,2,5,'VIEW',1.00,NULL,NULL,NULL,'2025-07-26 06:00:00',0),(34,2,5,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-26 06:05:00',0),(35,2,5,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 08:45:00',0),(36,2,10,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 03:30:00',0),(37,2,10,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-28 03:35:00',0),(38,2,10,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 05:20:00',0),(39,2,14,'VIEW',1.00,NULL,NULL,NULL,'2025-06-01 00:00:00',0),(40,2,14,'ENROLL',3.00,NULL,NULL,NULL,'2025-06-01 00:05:00',0),(41,2,14,'COMPLETE',5.00,NULL,NULL,NULL,'2025-06-10 09:30:00',0),(42,2,14,'LIKE',2.00,NULL,NULL,NULL,'2025-06-10 09:35:00',0),(43,2,2,'VIEW',1.00,NULL,NULL,NULL,'2025-07-27 07:20:00',0),(44,2,3,'VIEW',1.00,NULL,NULL,NULL,'2025-07-27 07:25:00',0),(45,2,6,'VIEW',1.00,NULL,NULL,NULL,'2025-07-27 07:30:00',0),(46,2,7,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 01:15:00',0),(47,2,8,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 01:20:00',0),(48,2,9,'VIEW',1.00,NULL,NULL,NULL,'2025-07-29 03:10:00',0),(49,2,11,'VIEW',1.00,NULL,NULL,NULL,'2025-07-29 03:15:00',0),(50,2,12,'VIEW',1.00,NULL,NULL,NULL,'2025-07-30 05:25:00',0),(51,2,13,'VIEW',1.00,NULL,NULL,NULL,'2025-07-30 05:30:00',0),(52,2,15,'VIEW',1.00,NULL,NULL,NULL,'2025-07-31 02:45:00',0),(53,2,16,'VIEW',1.00,NULL,NULL,NULL,'2025-07-31 02:50:00',0),(54,2,17,'VIEW',1.00,NULL,NULL,NULL,'2025-07-31 06:20:00',0),(55,2,18,'VIEW',1.00,NULL,NULL,NULL,'2025-07-31 06:25:00',0),(56,9,1,'VIEW',1.00,NULL,NULL,NULL,'2025-07-10 01:45:00',0),(57,9,1,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-10 01:50:00',0),(58,9,1,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 07:30:00',0),(59,9,6,'VIEW',1.00,NULL,NULL,NULL,'2025-06-25 02:00:00',0),(60,9,6,'ENROLL',3.00,NULL,NULL,NULL,'2025-06-25 02:05:00',0),(61,9,6,'COMPLETE',5.00,NULL,NULL,NULL,'2025-07-20 10:45:00',0),(62,9,6,'LIKE',2.00,NULL,NULL,NULL,'2025-07-20 10:50:00',0),(63,9,7,'VIEW',1.00,NULL,NULL,NULL,'2025-07-21 05:15:00',0),(64,9,7,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-21 05:20:00',0),(65,9,7,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 03:00:00',0),(66,9,16,'VIEW',1.00,NULL,NULL,NULL,'2025-07-15 08:30:00',0),(67,9,16,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-15 08:35:00',0),(68,9,16,'CLICK',1.00,NULL,NULL,NULL,'2025-07-30 06:15:00',0),(69,9,2,'VIEW',1.00,NULL,NULL,NULL,'2025-07-22 03:20:00',0),(70,9,3,'VIEW',1.00,NULL,NULL,NULL,'2025-07-22 03:25:00',0),(71,9,4,'VIEW',1.00,NULL,NULL,NULL,'2025-07-23 06:30:00',0),(72,9,5,'VIEW',1.00,NULL,NULL,NULL,'2025-07-23 06:35:00',0),(73,9,8,'VIEW',1.00,NULL,NULL,NULL,'2025-07-24 08:40:00',0),(74,9,9,'VIEW',1.00,NULL,NULL,NULL,'2025-07-24 08:45:00',0),(75,9,10,'VIEW',1.00,NULL,NULL,NULL,'2025-07-25 01:15:00',0),(76,9,11,'VIEW',1.00,NULL,NULL,NULL,'2025-07-25 01:20:00',0),(77,9,12,'VIEW',1.00,NULL,NULL,NULL,'2025-07-26 05:25:00',0),(78,9,13,'VIEW',1.00,NULL,NULL,NULL,'2025-07-26 05:30:00',0),(79,9,14,'VIEW',1.00,NULL,NULL,NULL,'2025-07-27 07:35:00',0),(80,9,15,'VIEW',1.00,NULL,NULL,NULL,'2025-07-27 07:40:00',0),(81,9,17,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 02:45:00',0),(82,9,18,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 02:50:00',0),(83,9,19,'VIEW',1.00,NULL,NULL,NULL,'2025-07-29 04:55:00',0),(84,9,20,'VIEW',1.00,NULL,NULL,NULL,'2025-07-29 05:00:00',0),(85,6,1,'VIEW',1.00,NULL,NULL,NULL,'2025-06-10 01:00:00',0),(86,6,1,'ENROLL',3.00,NULL,NULL,NULL,'2025-06-10 01:05:00',0),(87,6,1,'COMPLETE',5.00,NULL,NULL,NULL,'2025-06-25 09:30:00',0),(88,6,1,'LIKE',2.00,NULL,NULL,NULL,'2025-06-25 09:35:00',0),(89,6,4,'VIEW',1.00,NULL,NULL,NULL,'2025-06-26 02:15:00',0),(90,6,4,'ENROLL',3.00,NULL,NULL,NULL,'2025-06-26 02:20:00',0),(91,6,4,'CLICK',1.00,NULL,NULL,NULL,'2025-07-31 08:20:00',0),(92,6,14,'VIEW',1.00,NULL,NULL,NULL,'2025-05-15 00:30:00',0),(93,6,14,'ENROLL',3.00,NULL,NULL,NULL,'2025-05-15 00:35:00',0),(94,6,14,'COMPLETE',5.00,NULL,NULL,NULL,'2025-05-25 11:00:00',0),(95,6,14,'LIKE',2.00,NULL,NULL,NULL,'2025-05-25 11:05:00',0),(96,6,2,'VIEW',1.00,NULL,NULL,NULL,'2025-07-01 03:30:00',0),(97,6,3,'VIEW',1.00,NULL,NULL,NULL,'2025-07-01 03:35:00',0),(98,6,5,'VIEW',1.00,NULL,NULL,NULL,'2025-07-02 06:20:00',0),(99,6,6,'VIEW',1.00,NULL,NULL,NULL,'2025-07-02 06:25:00',0),(100,6,7,'VIEW',1.00,NULL,NULL,NULL,'2025-07-03 08:30:00',0),(101,6,8,'VIEW',1.00,NULL,NULL,NULL,'2025-07-03 08:35:00',0),(102,7,2,'VIEW',1.00,NULL,NULL,NULL,'2025-07-05 03:00:00',0),(103,7,2,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-05 03:05:00',0),(104,7,2,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 04:45:00',0),(105,7,3,'VIEW',1.00,NULL,NULL,NULL,'2025-06-01 01:30:00',0),(106,7,3,'ENROLL',3.00,NULL,NULL,NULL,'2025-06-01 01:35:00',0),(107,7,3,'COMPLETE',5.00,NULL,NULL,NULL,'2025-06-20 10:15:00',0),(108,7,3,'LIKE',2.00,NULL,NULL,NULL,'2025-06-20 10:20:00',0),(109,7,15,'VIEW',1.00,NULL,NULL,NULL,'2025-07-22 06:30:00',0),(110,7,15,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-22 06:35:00',0),(111,7,15,'CLICK',1.00,NULL,NULL,NULL,'2025-07-29 02:30:00',0),(112,7,1,'VIEW',1.00,NULL,NULL,NULL,'2025-07-10 05:20:00',0),(113,7,4,'VIEW',1.00,NULL,NULL,NULL,'2025-07-10 05:25:00',0),(114,7,5,'VIEW',1.00,NULL,NULL,NULL,'2025-07-11 07:30:00',0),(115,7,6,'VIEW',1.00,NULL,NULL,NULL,'2025-07-11 07:35:00',0),(116,7,8,'VIEW',1.00,NULL,NULL,NULL,'2025-07-12 09:40:00',0),(117,7,9,'VIEW',1.00,NULL,NULL,NULL,'2025-07-12 09:45:00',0),(118,8,5,'VIEW',1.00,NULL,NULL,NULL,'2025-06-15 02:45:00',0),(119,8,5,'ENROLL',3.00,NULL,NULL,NULL,'2025-06-15 02:50:00',0),(120,8,5,'COMPLETE',5.00,NULL,NULL,NULL,'2025-07-10 11:30:00',0),(121,8,5,'LIKE',2.00,NULL,NULL,NULL,'2025-07-10 11:35:00',0),(122,8,10,'VIEW',1.00,NULL,NULL,NULL,'2025-07-11 05:00:00',0),(123,8,10,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-11 05:05:00',0),(124,8,10,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 07:45:00',0),(125,8,18,'VIEW',1.00,NULL,NULL,NULL,'2025-07-25 08:15:00',0),(126,8,18,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-25 08:20:00',0),(127,8,18,'CLICK',1.00,NULL,NULL,NULL,'2025-07-28 03:30:00',0),(128,8,1,'VIEW',1.00,NULL,NULL,NULL,'2025-07-15 06:25:00',0),(129,8,2,'VIEW',1.00,NULL,NULL,NULL,'2025-07-15 06:30:00',0),(130,8,4,'VIEW',1.00,NULL,NULL,NULL,'2025-07-16 08:35:00',0),(131,8,6,'VIEW',1.00,NULL,NULL,NULL,'2025-07-16 08:40:00',0),(132,8,7,'VIEW',1.00,NULL,NULL,NULL,'2025-07-17 10:45:00',0),(133,8,8,'VIEW',1.00,NULL,NULL,NULL,'2025-07-17 10:50:00',0),(134,10,6,'VIEW',1.00,NULL,NULL,NULL,'2025-07-08 01:15:00',0),(135,10,6,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-08 01:20:00',0),(136,10,6,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 06:00:00',0),(137,10,7,'VIEW',1.00,NULL,NULL,NULL,'2025-07-18 03:45:00',0),(138,10,7,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-18 03:50:00',0),(139,10,7,'CLICK',1.00,NULL,NULL,NULL,'2025-07-31 08:30:00',0),(140,10,1,'VIEW',1.00,NULL,NULL,NULL,'2025-07-20 05:25:00',0),(141,10,2,'VIEW',1.00,NULL,NULL,NULL,'2025-07-20 05:30:00',0),(142,10,3,'VIEW',1.00,NULL,NULL,NULL,'2025-07-21 07:35:00',0),(143,10,4,'VIEW',1.00,NULL,NULL,NULL,'2025-07-21 07:40:00',0),(144,10,5,'VIEW',1.00,NULL,NULL,NULL,'2025-07-22 09:45:00',0),(145,10,8,'VIEW',1.00,NULL,NULL,NULL,'2025-07-22 09:50:00',0),(146,11,9,'VIEW',1.00,NULL,NULL,NULL,'2025-07-12 02:30:00',0),(147,11,9,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-12 02:35:00',0),(148,11,9,'CLICK',1.00,NULL,NULL,NULL,'2025-08-01 05:15:00',0),(149,11,12,'VIEW',1.00,NULL,NULL,NULL,'2025-07-20 07:00:00',0),(150,11,12,'ENROLL',3.00,NULL,NULL,NULL,'2025-07-20 07:05:00',0),(151,11,12,'CLICK',1.00,NULL,NULL,NULL,'2025-07-30 01:45:00',0),(152,11,1,'VIEW',1.00,NULL,NULL,NULL,'2025-07-25 03:20:00',0),(153,11,2,'VIEW',1.00,NULL,NULL,NULL,'2025-07-25 03:25:00',0),(154,11,3,'VIEW',1.00,NULL,NULL,NULL,'2025-07-26 05:30:00',0),(155,11,4,'VIEW',1.00,NULL,NULL,NULL,'2025-07-26 05:35:00',0),(156,11,5,'VIEW',1.00,NULL,NULL,NULL,'2025-07-27 07:40:00',0),(157,11,6,'VIEW',1.00,NULL,NULL,NULL,'2025-07-27 07:45:00',0),(158,11,7,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 09:50:00',0),(159,11,8,'VIEW',1.00,NULL,NULL,NULL,'2025-07-28 09:55:00',0);
/*!40000 ALTER TABLE `user_behaviors` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `nickname` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `role` enum('STUDENT','TEACHER','ADMIN') COLLATE utf8mb4_unicode_ci DEFAULT 'STUDENT',
  `status` enum('ACTIVE','INACTIVE','BANNED') COLLATE utf8mb4_unicode_ci DEFAULT 'ACTIVE',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'admin','<EMAIL>','$2a$10$.uchxSCoSEnCH6r3pI9Ln.KQpjlQFnGS2CpqwdkRVKSTOPmJlw3Ri','管理员',NULL,NULL,'ADMIN','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:33:55',0),(2,'user','<EMAIL>','$2a$10$JJ4g2POg7wfohtmazav7huTGTSow5vfGFG/wvahXVND1U3tXw5fX.','普通用户',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:34:16',0),(3,'123','<EMAIL>','$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq','1234',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(4,'teacher1','<EMAIL>','$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq','张老师',NULL,NULL,'TEACHER','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(5,'teacher2','<EMAIL>','$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq','李老师',NULL,NULL,'TEACHER','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(6,'student1','<EMAIL>','$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq','小明',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(7,'student2','<EMAIL>','$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq','小红',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(8,'student3','<EMAIL>','$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq','小刚',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(9,'student4','<EMAIL>','$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq','小丽',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(10,'student5','<EMAIL>','$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq','小华',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:30:29','2025-08-01 18:30:29',0),(11,'testuser','<EMAIL>','$2a$10$0/ScPuakPGk5VV3N5IomouENjolSYHxH6rCVXbB7dpzkSoPlfg6d2','Test User',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:33:19','2025-08-01 18:33:19',0),(12,'admin_temp','<EMAIL>','$2a$10$.uchxSCoSEnCH6r3pI9Ln.KQpjlQFnGS2CpqwdkRVKSTOPmJlw3Ri','Admin Temp',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:33:45','2025-08-01 18:33:45',0),(13,'user_temp','<EMAIL>','$2a$10$JJ4g2POg7wfohtmazav7huTGTSow5vfGFG/wvahXVND1U3tXw5fX.','User Temp',NULL,NULL,'STUDENT','ACTIVE','2025-08-01 18:34:06','2025-08-01 18:34:06',0);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-02 21:05:42
