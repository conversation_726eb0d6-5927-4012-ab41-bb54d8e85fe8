# SpringCloud微服务学习平台 - Windows一键启动脚本

## 环境要求

### 必需软件
1. **Java 22** - 已配置路径：`C:\Program Files\Java\jdk-22`
2. **Maven** - 需要添加到PATH环境变量
3. **Node.js** - 需要添加到PATH环境变量
4. **MySQL客户端** - 用于数据库初始化（可选）

### 数据库配置
- **服务器地址**: 192.168.226.100:3306
- **用户名**: root
- **密码**: mysql
- **数据库名**: learning_platform

## 使用方法

### 1. 数据库初始化（首次运行）
```cmd
cd database
run_init.bat
```
这将连接到远程MySQL服务器并初始化数据库。

### 2. 启动所有服务
```cmd
start-all-services.bat
```

### 3. 其他命令
```cmd
# 停止所有服务
start-all-services.bat stop

# 查看服务状态
start-all-services.bat status

# 重启所有服务
start-all-services.bat restart
```

### 4. 单独启动Nacos（可选）
```cmd
cd infrastructure\nacos
start-nacos.bat
```

## 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| Nacos | 8848 | 服务注册中心 |
| User Service | 8081 | 用户服务 |
| Course Service | 8082 | 课程服务 |
| Learning Service | 8083 | 学习服务 |
| Recommendation Service | 8084 | 推荐服务 |
| Discussion Service | 8085 | 讨论服务 |
| Payment Service | 8087 | 支付服务 |
| Gateway Service | 8090 | API网关 |
| Frontend | 8086 | 前端应用 |

## 访问地址

- **前端应用**: http://localhost:8086
- **API网关**: http://localhost:8090
- **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)

## 测试账号

- **管理员**: admin / admin
- **普通用户**: user / user
- **测试账号**: 123 / 123123

## 日志文件

启动后会在项目根目录生成以下日志文件：
- `user-service.log`
- `course-service.log`
- `learning-service.log`
- `recommendation-service.log`
- `discussion-service.log`
- `payment-service.log`
- `gateway-service.log`
- `frontend.log`

## 故障排除

### 1. Java路径问题
如果Java路径不是 `C:\Program Files\Java\jdk-22`，请修改 `start-all-services.bat` 文件中的 `JAVA_HOME` 变量。

### 2. 端口占用
如果某个端口被占用，脚本会显示警告。可以：
- 停止占用端口的程序
- 或修改相应服务的配置文件中的端口号

### 3. 数据库连接失败
检查：
- MySQL服务器是否运行
- 网络连接是否正常
- 用户名密码是否正确
- 防火墙设置

### 4. Maven/Node.js命令未找到
确保已将Maven和Node.js添加到系统PATH环境变量中。

## 注意事项

1. 首次运行需要下载依赖，可能需要较长时间
2. 确保有足够的内存（建议8GB以上）
3. 服务启动有先后顺序，请等待前一个服务完全启动后再启动下一个
4. 如果遇到问题，可以查看对应的日志文件进行排查
