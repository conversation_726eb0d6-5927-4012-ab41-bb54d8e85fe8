# SpringCloud微服务学习平台一键启动脚本 (Windows PowerShell版本)
# 作者: AI Assistant  
# 日期: 2025-08-01

# 设置错误处理
$ErrorActionPreference = "Stop"

# 强制指定Java路径
$env:JAVA_HOME = "C:\Program Files\Java\jdk-22"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"

# 强制指定MySQL配置
$env:MYSQL_HOST = "***************"
$env:MYSQL_PORT = "3306"
$env:MYSQL_USER = "root"
$env:MYSQL_PASSWORD = "mysql"

# 颜色定义函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 日志函数
function Log-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Green"
}

function Log-Warn {
    param([string]$Message)
    Write-ColorOutput "[WARN] $Message" "Yellow"
}

function Log-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Log-Step {
    param([string]$Message)
    Write-ColorOutput "[STEP] $Message" "Blue"
}

# 检查命令是否存在
function Test-Command {
    param([string]$CommandName)
    try {
        Get-Command $CommandName -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# 检查端口是否被占用
function Test-Port {
    param(
        [int]$Port,
        [string]$ServiceName
    )
    try {
        $connection = Get-NetTCPConnection -LocalPort $Port -State Listen -ErrorAction SilentlyContinue
        if ($connection) {
            Log-Warn "端口 $Port 已被占用 ($ServiceName)"
            return $false
        }
        return $true
    }
    catch {
        return $true
    }
}

# 等待服务启动
function Wait-ForService {
    param(
        [string]$Url,
        [string]$ServiceName,
        [int]$MaxAttempts = 30
    )
    
    Log-Info "等待 $ServiceName 启动..."
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Log-Info "$ServiceName 启动成功！"
                return $true
            }
        }
        catch {
            # 继续等待
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
    }
    
    Write-Host ""
    Log-Error "$ServiceName 启动超时！"
    return $false
}

# 等待端口可用
function Wait-ForPort {
    param(
        [int]$Port,
        [string]$ServiceName,
        [int]$MaxAttempts = 30
    )
    
    Log-Info "等待 $ServiceName 端口 $Port 启动..."
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        try {
            $connection = Get-NetTCPConnection -LocalPort $Port -State Listen -ErrorAction SilentlyContinue
            if ($connection) {
                Log-Info "$ServiceName 端口 $Port 已启动！"
                return $true
            }
        }
        catch {
            # 继续等待
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
    }
    
    Write-Host ""
    Log-Error "$ServiceName 端口 $Port 启动超时"
    return $false
}

# 停止所有服务
function Stop-AllServices {
    Log-Step "停止所有服务..."
    
    # 停止前端服务
    Get-Process | Where-Object { $_.ProcessName -like "*node*" -and $_.CommandLine -like "*vue-cli-service*" } | Stop-Process -Force -ErrorAction SilentlyContinue
    
    # 停止Spring Boot服务
    Get-Process | Where-Object { $_.ProcessName -like "*java*" -and ($_.CommandLine -like "*spring-boot*" -or $_.CommandLine -like "*gateway-service*" -or $_.CommandLine -like "*user-service*" -or $_.CommandLine -like "*course-service*" -or $_.CommandLine -like "*learning-service*" -or $_.CommandLine -like "*recommendation-service*" -or $_.CommandLine -like "*discussion-service*" -or $_.CommandLine -like "*payment-service*") } | Stop-Process -Force -ErrorAction SilentlyContinue
    
    # 停止Nacos
    if (Test-Path "infrastructure\nacos\nacos\bin\shutdown.cmd") {
        Push-Location "infrastructure\nacos\nacos\bin"
        try {
            & ".\shutdown.cmd" 2>$null
        }
        catch {
            # 忽略错误
        }
        Pop-Location
    }
    
    Log-Info "所有服务已停止"
}

# 环境检查
function Test-Environment {
    Log-Step "检查运行环境..."

    # 检查Java（使用强制指定的路径）
    $javaExe = "C:\Program Files\Java\jdk-22\bin\java.exe"
    if (-not (Test-Path $javaExe)) {
        Log-Error "未找到Java，请确保Java安装在: C:\Program Files\Java\jdk-22\"
        exit 1
    }

    try {
        $javaVersion = & $javaExe -version 2>&1 | Select-String "version" | Select-Object -First 1
        Log-Info "Java版本检查通过: $javaVersion"
    }
    catch {
        Log-Error "Java版本检查失败"
        exit 1
    }

    # 检查Maven
    if (-not (Test-Command "mvn")) {
        Log-Error "请先安装Maven并添加到PATH环境变量"
        exit 1
    }
    $mvnVersion = mvn -version | Select-Object -First 1
    Log-Info "Maven版本: $mvnVersion"

    # 检查Node.js
    if (-not (Test-Command "node")) {
        Log-Error "请先安装Node.js"
        exit 1
    }
    $nodeVersion = node -v
    Log-Info "Node.js版本: $nodeVersion"

    # 检查npm
    if (-not (Test-Command "npm")) {
        Log-Error "请先安装npm"
        exit 1
    }
    $npmVersion = npm -v
    Log-Info "npm版本: $npmVersion"

    # MySQL配置信息
    Log-Info "MySQL配置: $env:MYSQL_HOST`:$env:MYSQL_PORT (用户: $env:MYSQL_USER)"
}

# 编译项目
function Build-Project {
    Log-Step "编译项目..."
    
    if (-not (Test-Path "pom.xml")) {
        Log-Error "未找到pom.xml文件，请确保在项目根目录运行此脚本"
        exit 1
    }
    
    Log-Info "清理并编译项目..."
    try {
        & mvn clean compile -DskipTests=true
        if ($LASTEXITCODE -ne 0) {
            throw "Maven编译失败"
        }
    }
    catch {
        Log-Error "项目编译失败: $_"
        exit 1
    }
    
    Log-Info "项目编译成功"
}

# 安装前端依赖
function Install-FrontendDeps {
    Log-Step "安装前端依赖..."
    
    if (-not (Test-Path "frontend")) {
        Log-Error "未找到frontend目录"
        exit 1
    }
    
    Push-Location "frontend"
    
    if (-not (Test-Path "package.json")) {
        Log-Error "未找到package.json文件"
        Pop-Location
        exit 1
    }
    
    Log-Info "安装前端依赖..."
    try {
        & npm install
        if ($LASTEXITCODE -ne 0) {
            throw "npm install失败"
        }
    }
    catch {
        Log-Error "前端依赖安装失败: $_"
        Pop-Location
        exit 1
    }
    
    Pop-Location
    Log-Info "前端依赖安装成功"
}

# 检查Nacos是否完整安装
function Test-NacosInstallation {
    $nacosJar = "infrastructure\nacos\nacos\target\nacos-server.jar"
    $nacosbin = "infrastructure\nacos\nacos\bin"
    $nacosStartup = "infrastructure\nacos\nacos\bin\startup.cmd"

    if (-not (Test-Path $nacosJar)) {
        Log-Warn "Nacos安装不完整，缺少 nacos-server.jar 文件"
        return $false
    }

    if (-not (Test-Path $nacosbin)) {
        Log-Warn "Nacos安装不完整，缺少 bin 目录"
        return $false
    }

    if (-not (Test-Path $nacosStartup)) {
        Log-Warn "Nacos安装不完整，缺少启动脚本"
        return $false
    }

    return $true
}

# 下载并安装Nacos
function Install-Nacos {
    $nacosVersion = "2.3.0"
    $nacosUrl = "https://github.com/alibaba/nacos/releases/download/$nacosVersion/nacos-server-$nacosVersion.tar.gz"
    $nacosDir = "infrastructure\nacos"

    Log-Step "自动下载和安装Nacos $nacosVersion..."

    # 备份现有配置（如果存在）
    $configFile = "$nacosDir\nacos\conf\application.properties"
    $backupFile = "$nacosDir\application.properties.backup"
    if (Test-Path $configFile) {
        Log-Info "备份现有配置文件..."
        Copy-Item $configFile $backupFile -Force
    }

    Push-Location $nacosDir

    # 下载Nacos
    Log-Info "正在下载 nacos-server-$nacosVersion.tar.gz..."
    $downloadFile = "nacos-server-$nacosVersion.tar.gz"

    try {
        if (Test-Command "curl") {
            & curl -L -O $nacosUrl
        }
        elseif (Test-Command "wget") {
            & wget $nacosUrl
        }
        else {
            # 使用PowerShell内置的Invoke-WebRequest
            Invoke-WebRequest -Uri $nacosUrl -OutFile $downloadFile
        }
    }
    catch {
        Log-Error "下载Nacos失败: $_"
        Pop-Location
        exit 1
    }

    # 解压Nacos
    Log-Info "正在解压Nacos..."
    if (Test-Path "nacos") {
        Remove-Item "nacos" -Recurse -Force
    }

    # 使用tar命令解压（Windows 10 1803+支持）
    try {
        & tar -xzf $downloadFile
    }
    catch {
        Log-Error "解压Nacos失败，请确保系统支持tar命令或手动解压"
        Pop-Location
        exit 1
    }

    # 恢复配置文件
    if (Test-Path $backupFile) {
        Log-Info "恢复配置文件..."
        Copy-Item $backupFile "nacos\conf\application.properties" -Force
        Remove-Item $backupFile -Force
    }

    # 清理下载文件
    Remove-Item $downloadFile -Force

    Pop-Location

    Log-Info "Nacos $nacosVersion 安装完成"
}

# 启动Nacos
function Start-Nacos {
    Log-Step "启动Nacos注册中心..."

    if (-not (Test-Port 8848 "Nacos")) {
        Log-Info "Nacos已在运行"
        return $true
    }

    # 检查Nacos安装
    if (-not (Test-NacosInstallation)) {
        Install-Nacos
    }

    if (-not (Test-Path "infrastructure\nacos\nacos\bin\startup.cmd")) {
        Log-Error "Nacos安装失败，未找到启动脚本"
        exit 1
    }

    Push-Location "infrastructure\nacos\nacos\bin"

    # 设置JVM参数
    $env:JAVA_OPT = "-server -Xms512m -Xmx512m -Xmn256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m"

    # 启动Nacos
    try {
        Start-Process -FilePath "startup.cmd" -ArgumentList "-m standalone" -WindowStyle Hidden
    }
    catch {
        Log-Error "启动Nacos失败: $_"
        Pop-Location
        exit 1
    }

    Pop-Location

    # 等待Nacos启动
    $result = Wait-ForService "http://localhost:8848/nacos" "Nacos"
    if (-not $result) {
        exit 1
    }

    Log-Info "Nacos管理界面: http://localhost:8848/nacos (用户名/密码: nacos/nacos)"
    return $true
}

# 启动微服务
function Start-Microservice {
    param(
        [string]$ServiceName,
        [int]$Port
    )

    Log-Step "启动 $ServiceName..."

    if (-not (Test-Port $Port $ServiceName)) {
        Log-Info "$ServiceName 已在运行"
        return $true
    }

    if (-not (Test-Path $ServiceName)) {
        Log-Error "未找到 $ServiceName 目录"
        return $false
    }

    Push-Location $ServiceName

    # 后台启动服务
    try {
        $logFile = "..\$ServiceName.log"
        Start-Process -FilePath "mvn" -ArgumentList "spring-boot:run" -WindowStyle Hidden -RedirectStandardOutput $logFile -RedirectStandardError $logFile
    }
    catch {
        Log-Error "启动 $ServiceName 失败: $_"
        Pop-Location
        return $false
    }

    Pop-Location

    # 等待服务启动
    Start-Sleep -Seconds 5
    $result = Wait-ForPort $Port $ServiceName

    if ($result) {
        Log-Info "$ServiceName 启动成功，端口: $Port"
        return $true
    }
    else {
        return $false
    }
}

# 启动前端
function Start-Frontend {
    Log-Step "启动前端服务..."

    if (-not (Test-Port 8086 "Frontend")) {
        Log-Info "前端服务已在运行"
        return $true
    }

    Push-Location "frontend"

    # 后台启动前端
    try {
        $logFile = "..\frontend.log"
        Start-Process -FilePath "npm" -ArgumentList "run serve" -WindowStyle Hidden -RedirectStandardOutput $logFile -RedirectStandardError $logFile
    }
    catch {
        Log-Error "启动前端服务失败: $_"
        Pop-Location
        return $false
    }

    Pop-Location

    # 等待前端启动
    Start-Sleep -Seconds 10
    $result = Wait-ForService "http://localhost:8086" "Frontend"

    if ($result) {
        Log-Info "前端服务启动成功"
        Log-Info "前端访问地址: http://localhost:8086"
        return $true
    }
    else {
        return $false
    }
}

# 显示服务状态
function Show-ServiceStatus {
    Log-Step "服务状态检查..."

    Write-Host ""
    Write-Host "=== 服务状态 ===" -ForegroundColor Cyan

    $services = @(
        @{Name="Nacos"; Port=8848},
        @{Name="User-Service"; Port=8081},
        @{Name="Course-Service"; Port=8082},
        @{Name="Learning-Service"; Port=8083},
        @{Name="Recommendation-Service"; Port=8084},
        @{Name="Discussion-Service"; Port=8085},
        @{Name="Gateway-Service"; Port=8090},
        @{Name="Frontend"; Port=8086}
    )

    foreach ($service in $services) {
        try {
            $connection = Get-NetTCPConnection -LocalPort $service.Port -State Listen -ErrorAction SilentlyContinue
            if ($connection) {
                Write-ColorOutput "✓ $($service.Name) (端口 $($service.Port)) - 运行中" "Green"
            }
            else {
                Write-ColorOutput "✗ $($service.Name) (端口 $($service.Port)) - 未运行" "Red"
            }
        }
        catch {
            Write-ColorOutput "✗ $($service.Name) (端口 $($service.Port)) - 未运行" "Red"
        }
    }

    Write-Host ""
    Write-Host "=== 访问地址 ===" -ForegroundColor Cyan
    Write-Host "• 前端应用: http://localhost:8086"
    Write-Host "• API网关: http://localhost:8090"
    Write-Host "• Nacos控制台: http://localhost:8848/nacos (nacos/nacos)"
    Write-Host ""
    Write-Host "=== 日志文件 ===" -ForegroundColor Cyan
    Write-Host "• 各服务日志: .\{service-name}.log"
    Write-Host "• 前端日志: .\frontend.log"
}

# 主函数
function Main {
    param([string]$Action = "start")

    Write-Host "==========================================" -ForegroundColor Cyan
    Write-Host "  SpringCloud微服务学习平台启动脚本" -ForegroundColor Cyan
    Write-Host "==========================================" -ForegroundColor Cyan
    Write-Host ""

    # 处理命令行参数
    switch ($Action.ToLower()) {
        "stop" {
            Stop-AllServices
            return
        }
        "status" {
            Show-ServiceStatus
            return
        }
        "restart" {
            Stop-AllServices
            Start-Sleep -Seconds 3
            # 继续执行启动流程
        }
        "start" {
            # 继续执行启动流程
        }
        default {
            Write-Host "用法: .\start-all-services.ps1 [start|stop|restart|status]"
            Write-Host "  start   - 启动所有服务 (默认)"
            Write-Host "  stop    - 停止所有服务"
            Write-Host "  restart - 重启所有服务"
            Write-Host "  status  - 查看服务状态"
            return
        }
    }

    # 环境检查
    Test-Environment

    # 编译项目
    Build-Project

    # 安装前端依赖
    Install-FrontendDeps

    # 启动服务
    $nacosResult = Start-Nacos
    if (-not $nacosResult) {
        Log-Error "Nacos启动失败，停止启动流程"
        return
    }

    # 等待Nacos完全启动
    Start-Sleep -Seconds 5

    # 按顺序启动微服务
    $services = @(
        @{Name="user-service"; Port=8081},
        @{Name="course-service"; Port=8082},
        @{Name="learning-service"; Port=8083},
        @{Name="recommendation-service"; Port=8084},
        @{Name="discussion-service"; Port=8085},
        @{Name="payment-service"; Port=8087},
        @{Name="gateway-service"; Port=8090}
    )

    foreach ($service in $services) {
        $result = Start-Microservice $service.Name $service.Port
        if (-not $result) {
            Log-Warn "$($service.Name) 启动失败，但继续启动其他服务"
        }
    }

    # 启动前端
    $frontendResult = Start-Frontend
    if (-not $frontendResult) {
        Log-Warn "前端服务启动失败"
    }

    # 显示服务状态
    Show-ServiceStatus

    Log-Info "启动流程完成！"
    Log-Info "按 Ctrl+C 可以退出，或运行 '.\start-all-services.ps1 stop' 停止所有服务"
}

# 捕获Ctrl+C信号
$null = Register-EngineEvent PowerShell.Exiting -Action {
    Write-Host "`n提示: 服务仍在后台运行，使用 .\start-all-services.ps1 stop 停止所有服务" -ForegroundColor Yellow
}

# 运行主函数
if ($args.Count -gt 0) {
    Main $args[0]
}
else {
    Main "start"
}
