-- 学习平台数据库初始化脚本 (增强版)
-- 包含50个课程和丰富的推荐算法数据
-- 创建时间: 2025-08-02

-- 创建数据库
DROP DATABASE IF EXISTS learning_platform;
CREATE DATABASE learning_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE learning_platform;

-- 创建用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    avatar_url VARCHAR(255),
    phone VARCHAR(20),
    role ENUM('STUDENT', 'TEACHER', 'ADMIN') DEFAULT 'STUDENT',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0
);

-- 创建课程分类表
CREATE TABLE course_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_parent (parent_id)
);

-- 创建课程表
CREATE TABLE courses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    content TEXT,
    teacher_id BIGINT NOT NULL,
    category_id BIGINT,
    difficulty_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED') DEFAULT 'BEGINNER',
    cover_image VARCHAR(255),
    duration_hours INT DEFAULT 0,
    price DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'PUBLISHED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_category (category_id),
    INDEX idx_teacher (teacher_id),
    INDEX idx_status (status),
    FOREIGN KEY (category_id) REFERENCES course_categories(id),
    FOREIGN KEY (teacher_id) REFERENCES users(id)
);

-- 创建课程章节表
CREATE TABLE course_chapters (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    course_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content_type VARCHAR(20) DEFAULT 'VIDEO',
    content_url VARCHAR(500),
    duration_minutes INT DEFAULT 0,
    order_index INT DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_course (course_id),
    INDEX idx_order (order_index),
    FOREIGN KEY (course_id) REFERENCES courses(id)
);

-- 创建课程注册表
CREATE TABLE course_enrollments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    deleted TINYINT(1) DEFAULT 0,
    CONSTRAINT unique_enrollment UNIQUE (user_id, course_id),
    INDEX idx_user (user_id),
    INDEX idx_course (course_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (course_id) REFERENCES courses(id)
);

-- 创建学习进度表
CREATE TABLE learning_progress (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    chapter_id BIGINT NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    watch_duration_seconds INT DEFAULT 0,
    last_position_seconds INT DEFAULT 0,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user_course (user_id, course_id),
    INDEX idx_chapter (chapter_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (course_id) REFERENCES courses(id),
    FOREIGN KEY (chapter_id) REFERENCES course_chapters(id)
);

-- 创建测验记录表
CREATE TABLE quiz_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    chapter_id BIGINT NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    total_score DECIMAL(5,2) NOT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user (user_id),
    INDEX idx_chapter (chapter_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (chapter_id) REFERENCES course_chapters(id)
);

-- 创建讨论主题表
CREATE TABLE discussion_topics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    course_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    view_count INT DEFAULT 0,
    reply_count INT DEFAULT 0,
    last_reply_at TIMESTAMP NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_locked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_course (course_id),
    INDEX idx_user (user_id),
    INDEX idx_created (created_at),
    FOREIGN KEY (course_id) REFERENCES courses(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建讨论回复表
CREATE TABLE discussion_replies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    topic_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    parent_id BIGINT NULL,
    like_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_topic (topic_id),
    INDEX idx_user (user_id),
    INDEX idx_parent (parent_id),
    INDEX idx_created (created_at),
    FOREIGN KEY (topic_id) REFERENCES discussion_topics(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (parent_id) REFERENCES discussion_replies(id)
);

-- 创建学习记录表
CREATE TABLE learning_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    progress DECIMAL(5,2) DEFAULT 0.00,
    status ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'PAUSED') DEFAULT 'NOT_STARTED',
    start_time TIMESTAMP NULL,
    completion_time TIMESTAMP NULL,
    last_access_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    UNIQUE KEY uk_user_course (user_id, course_id),
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_status (status)
);

-- 创建用户行为表 (推荐算法用)
CREATE TABLE user_behaviors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    behavior_type ENUM('VIEW', 'ENROLL', 'COMPLETE', 'RATE', 'FEEDBACK') NOT NULL,
    behavior_value DECIMAL(3,1) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_created_at (created_at)
);

-- 创建推荐表
CREATE TABLE recommendations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    algorithm_type ENUM('COLLABORATIVE', 'CONTENT_BASED', 'HYBRID') NOT NULL,
    score DECIMAL(5,4) NOT NULL,
    reason VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_algorithm_type (algorithm_type),
    INDEX idx_score (score),
    INDEX idx_created_at (created_at)
);

-- 插入课程分类数据
INSERT INTO course_categories (id, name, description, parent_id, created_at, updated_at, deleted) VALUES
(1, '编程开发', '编程和软件开发相关课程', NULL, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(2, '前端开发', '前端技术和框架相关课程', 1, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(3, '后端开发', '后端技术和框架相关课程', 1, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(4, '数据库技术', '数据库设计和管理相关课程', 1, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(5, '人工智能', 'AI和机器学习相关课程', NULL, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(6, '数据科学', '数据分析和数据科学相关课程', 5, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0);

-- 插入用户数据 (保留现有密码哈希)
INSERT INTO users (id, username, email, password, nickname, role, status) VALUES
(1, 'admin', '<EMAIL>', '$2a$10$.uchxSCoSEnCH6r3pI9Ln.KQpjlQFnGS2CpqwdkRVKSTOPmJlw3Ri', '管理员', 'ADMIN', 'ACTIVE'),
(2, 'user', '<EMAIL>', '$2a$10$JJ4g2POg7wfohtmazav7huTGTSow5vfGFG/wvahXVND1U3tXw5fX.', '普通用户', 'STUDENT', 'ACTIVE'),
(3, 'teacher1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '张教授', 'TEACHER', 'ACTIVE'),
(4, 'teacher2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '李老师', 'TEACHER', 'ACTIVE'),
(5, 'teacher3', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '王博士', 'TEACHER', 'ACTIVE'),
(6, 'teacher4', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '陈老师', 'TEACHER', 'ACTIVE'),
(7, 'teacher5', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '刘教授', 'TEACHER', 'ACTIVE'),
(8, 'student1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小明', 'STUDENT', 'ACTIVE'),
(9, 'student2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小红', 'STUDENT', 'ACTIVE'),
(10, 'student3', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小李', 'STUDENT', 'ACTIVE');

-- 插入50个课程数据
INSERT INTO courses (id, title, description, content, teacher_id, category_id, difficulty_level, duration_hours, price, status) VALUES
-- 编程基础类 (1-10)
(1, 'Java编程基础', 'Java编程语言基础课程，从零开始学习Java', 'Java语法、面向对象编程、集合框架、异常处理等基础知识', 3, 1, 'BEGINNER', 40, 199.00, 'PUBLISHED'),
(2, 'Python入门到精通', 'Python编程语言完整学习路径', 'Python语法、数据结构、函数、模块、面向对象编程', 4, 1, 'BEGINNER', 45, 179.00, 'PUBLISHED'),
(3, 'C++程序设计', 'C++编程语言系统学习', 'C++语法、指针、类与对象、STL标准库', 5, 1, 'INTERMEDIATE', 50, 249.00, 'PUBLISHED'),
(4, 'JavaScript现代开发', 'JavaScript ES6+现代开发技术', 'ES6语法、异步编程、模块化、函数式编程', 3, 1, 'INTERMEDIATE', 35, 219.00, 'PUBLISHED'),
(5, 'Go语言实战', 'Go语言从入门到实战项目', 'Go语法、并发编程、网络编程、微服务开发', 6, 1, 'INTERMEDIATE', 42, 289.00, 'PUBLISHED'),
(6, 'Rust系统编程', 'Rust语言系统级编程', 'Rust语法、内存管理、并发安全、系统编程', 7, 1, 'ADVANCED', 48, 329.00, 'PUBLISHED'),
(7, 'Kotlin移动开发', 'Kotlin语言Android开发', 'Kotlin语法、Android开发、UI设计、数据存储', 4, 1, 'INTERMEDIATE', 38, 259.00, 'PUBLISHED'),
(8, 'Swift iOS开发', 'Swift语言iOS应用开发', 'Swift语法、iOS开发、UIKit、SwiftUI', 5, 1, 'INTERMEDIATE', 44, 279.00, 'PUBLISHED'),
(9, 'PHP Web开发', 'PHP服务器端开发技术', 'PHP语法、MySQL数据库、Laravel框架', 6, 1, 'BEGINNER', 36, 189.00, 'PUBLISHED'),
(10, 'Ruby on Rails', 'Ruby语言Web开发框架', 'Ruby语法、Rails框架、MVC架构、RESTful API', 7, 1, 'INTERMEDIATE', 40, 269.00, 'PUBLISHED'),

-- 前端开发类 (11-20)
(11, 'HTML5+CSS3基础', 'Web前端基础技术学习', 'HTML5语义化、CSS3样式、响应式设计、Flexbox布局', 3, 2, 'BEGINNER', 30, 149.00, 'PUBLISHED'),
(12, 'Vue.js 3.0实战', 'Vue.js现代前端框架开发', 'Vue3组合式API、路由管理、状态管理、组件开发', 4, 2, 'INTERMEDIATE', 45, 299.00, 'PUBLISHED'),
(13, 'React开发进阶', 'React前端框架深度学习', 'React Hooks、状态管理、性能优化、测试', 5, 2, 'INTERMEDIATE', 48, 319.00, 'PUBLISHED'),
(14, 'Angular企业级开发', 'Angular框架企业应用开发', 'Angular架构、依赖注入、RxJS、企业级项目', 6, 2, 'ADVANCED', 52, 359.00, 'PUBLISHED'),
(15, 'TypeScript高级编程', 'TypeScript静态类型编程', 'TypeScript语法、类型系统、装饰器、高级特性', 3, 2, 'INTERMEDIATE', 35, 239.00, 'PUBLISHED'),
(16, 'Webpack构建工具', '现代前端构建工具学习', 'Webpack配置、模块打包、性能优化、插件开发', 4, 2, 'INTERMEDIATE', 28, 199.00, 'PUBLISHED'),
(17, 'Sass/Less预处理器', 'CSS预处理器技术', 'Sass语法、Less语法、模块化CSS、构建集成', 5, 2, 'BEGINNER', 20, 129.00, 'PUBLISHED'),
(18, 'Bootstrap响应式设计', 'Bootstrap前端UI框架', 'Bootstrap组件、栅格系统、响应式设计、定制化', 6, 2, 'BEGINNER', 25, 159.00, 'PUBLISHED'),
(19, 'Node.js全栈开发', 'Node.js服务器端JavaScript', 'Node.js基础、Express框架、数据库集成、API开发', 7, 2, 'INTERMEDIATE', 42, 289.00, 'PUBLISHED'),
(20, 'Electron桌面应用', 'Electron跨平台桌面应用开发', 'Electron架构、主进程、渲染进程、原生API', 3, 2, 'ADVANCED', 38, 279.00, 'PUBLISHED'),

-- 后端开发类 (21-30)
(21, 'Spring Boot微服务', 'Spring Boot企业级开发', 'Spring Boot核心、微服务架构、云原生开发', 4, 3, 'INTERMEDIATE', 55, 349.00, 'PUBLISHED'),
(22, 'Django Web框架', 'Python Django Web开发', 'Django架构、ORM、模板系统、RESTful API', 5, 3, 'INTERMEDIATE', 48, 299.00, 'PUBLISHED'),
(23, 'Express.js API开发', 'Node.js Express框架开发', 'Express路由、中间件、数据库集成、API设计', 6, 3, 'INTERMEDIATE', 35, 229.00, 'PUBLISHED'),
(24, 'ASP.NET Core开发', '.NET Core跨平台开发', '.NET Core架构、Web API、Entity Framework、部署', 7, 3, 'INTERMEDIATE', 50, 329.00, 'PUBLISHED'),
(25, 'Ruby on Rails API', 'Rails API后端开发', 'Rails API模式、JSON序列化、认证授权、测试', 3, 'Backend', 'INTERMEDIATE', 40, 269.00, 'PUBLISHED'),
(26, 'FastAPI Python框架', 'FastAPI现代Python Web框架', 'FastAPI特性、异步编程、自动文档、性能优化', 4, 'Backend', 'INTERMEDIATE', 32, 219.00, 'PUBLISHED'),
(27, 'Gin Go Web框架', 'Go语言Gin框架开发', 'Gin路由、中间件、JSON处理、性能优化', 5, 'Backend', 'INTERMEDIATE', 30, 199.00, 'PUBLISHED'),
(28, 'Laravel PHP框架', 'Laravel现代PHP开发', 'Laravel架构、Eloquent ORM、Blade模板、队列', 6, 'Backend', 'INTERMEDIATE', 45, 289.00, 'PUBLISHED'),
(29, 'Koa.js轻量级框架', 'Koa.js Node.js轻量级框架', 'Koa中间件、异步流程控制、错误处理', 7, 'Backend', 'INTERMEDIATE', 28, 189.00, 'PUBLISHED'),
(30, 'Nest.js企业级框架', 'Nest.js TypeScript企业级框架', 'Nest.js架构、依赖注入、装饰器、微服务', 3, 'Backend', 'ADVANCED', 48, 339.00, 'PUBLISHED'),

-- 数据库类 (31-40)
(31, 'MySQL数据库设计', 'MySQL数据库设计与优化', 'SQL语法、数据库设计、索引优化、性能调优', 4, 'Database', 'INTERMEDIATE', 40, 249.00, 'PUBLISHED'),
(32, 'PostgreSQL高级应用', 'PostgreSQL数据库高级特性', 'PostgreSQL特性、JSON支持、全文搜索、扩展', 5, 'Database', 'ADVANCED', 45, 289.00, 'PUBLISHED'),
(33, 'MongoDB文档数据库', 'MongoDB NoSQL数据库', 'MongoDB基础、文档模型、聚合管道、分片', 6, 'Database', 'INTERMEDIATE', 35, 219.00, 'PUBLISHED'),
(34, 'Redis缓存技术', 'Redis内存数据库应用', 'Redis数据结构、缓存策略、集群部署、性能优化', 7, 'Database', 'INTERMEDIATE', 30, 199.00, 'PUBLISHED'),
(35, 'Elasticsearch搜索', 'Elasticsearch全文搜索引擎', 'ES基础、索引设计、查询DSL、集群管理', 3, 'Database', 'ADVANCED', 42, 299.00, 'PUBLISHED'),
(36, 'Oracle数据库管理', 'Oracle企业级数据库', 'Oracle架构、PL/SQL、性能调优、备份恢复', 4, 'Database', 'ADVANCED', 55, 399.00, 'PUBLISHED'),
(37, 'SQLite轻量级数据库', 'SQLite嵌入式数据库', 'SQLite特性、移动应用集成、性能优化', 5, 'Database', 'BEGINNER', 20, 129.00, 'PUBLISHED'),
(38, 'InfluxDB时序数据库', 'InfluxDB时间序列数据库', 'InfluxDB架构、时序数据处理、监控应用', 6, 'Database', 'INTERMEDIATE', 28, 189.00, 'PUBLISHED'),
(39, 'Neo4j图数据库', 'Neo4j图数据库应用', 'Neo4j基础、Cypher查询、图算法、社交网络', 7, 'Database', 'ADVANCED', 38, 269.00, 'PUBLISHED'),
(40, 'Cassandra分布式数据库', 'Cassandra大数据存储', 'Cassandra架构、数据模型、集群管理、调优', 3, 'Database', 'ADVANCED', 48, 329.00, 'PUBLISHED'),

-- 人工智能与数据科学类 (41-50)
(41, 'Python数据分析', 'Python数据分析基础', 'Pandas、NumPy、Matplotlib、数据清洗与可视化', 4, 'DataScience', 'BEGINNER', 35, 229.00, 'PUBLISHED'),
(42, '机器学习入门', '机器学习算法与应用', '监督学习、无监督学习、模型评估、Scikit-learn', 5, 'AI', 'INTERMEDIATE', 50, 349.00, 'PUBLISHED'),
(43, '深度学习与神经网络', '深度学习技术详解', '神经网络、CNN、RNN、TensorFlow、PyTorch', 6, 'AI', 'ADVANCED', 60, 449.00, 'PUBLISHED'),
(44, '自然语言处理', 'NLP技术与应用', '文本处理、词向量、BERT、GPT、情感分析', 7, 'AI', 'ADVANCED', 55, 399.00, 'PUBLISHED'),
(45, '计算机视觉', '计算机视觉算法', '图像处理、目标检测、人脸识别、OpenCV', 3, 'AI', 'ADVANCED', 52, 379.00, 'PUBLISHED'),
(46, '数据挖掘技术', '数据挖掘算法与实践', '关联规则、聚类分析、分类算法、推荐系统', 4, 'DataScience', 'INTERMEDIATE', 45, 299.00, 'PUBLISHED'),
(47, '大数据处理', 'Hadoop与Spark大数据', 'Hadoop生态、Spark计算、数据仓库、ETL', 5, 'DataScience', 'ADVANCED', 58, 419.00, 'PUBLISHED'),
(48, '统计学与概率论', '数据科学数学基础', '描述统计、推断统计、概率分布、假设检验', 6, 'DataScience', 'INTERMEDIATE', 40, 249.00, 'PUBLISHED'),
(49, 'R语言数据分析', 'R语言统计分析', 'R语法、数据处理、统计建模、可视化', 7, 'DataScience', 'INTERMEDIATE', 38, 229.00, 'PUBLISHED'),
(50, '强化学习', '强化学习算法与应用', 'Q-learning、策略梯度、深度强化学习、游戏AI', 3, 'AI', 'ADVANCED', 48, 369.00, 'PUBLISHED');

-- 插入学习记录数据
INSERT INTO learning_records (user_id, course_id, progress, status, start_time, completion_time, last_access_time) VALUES
-- admin用户 (ID=1) 的学习记录
(1, 1, 100.00, 'COMPLETED', '2025-07-01 09:00:00', '2025-07-15 18:30:00', '2025-07-15 18:30:00'),
(1, 2, 85.50, 'IN_PROGRESS', '2025-07-16 10:00:00', NULL, '2025-08-01 14:20:00'),
(1, 3, 100.00, 'COMPLETED', '2025-06-15 08:30:00', '2025-06-30 16:45:00', '2025-06-30 16:45:00'),
(1, 11, 75.20, 'IN_PROGRESS', '2025-07-20 11:00:00', NULL, '2025-08-01 09:15:00'),
(1, 12, 60.30, 'IN_PROGRESS', '2025-07-25 13:30:00', NULL, '2025-07-31 17:00:00'),
(1, 21, 45.80, 'IN_PROGRESS', '2025-07-28 14:00:00', NULL, '2025-08-01 16:30:00'),
(1, 31, 100.00, 'COMPLETED', '2025-06-01 10:00:00', '2025-06-20 17:00:00', '2025-06-20 17:00:00'),
(1, 41, 90.50, 'IN_PROGRESS', '2025-07-10 09:30:00', NULL, '2025-08-01 11:45:00'),

-- user用户 (ID=2) 的学习记录
(2, 1, 95.00, 'IN_PROGRESS', '2025-07-05 10:30:00', NULL, '2025-08-01 15:20:00'),
(2, 4, 100.00, 'COMPLETED', '2025-06-20 09:00:00', '2025-07-10 18:00:00', '2025-07-10 18:00:00'),
(2, 11, 100.00, 'COMPLETED', '2025-06-10 08:00:00', '2025-06-25 17:30:00', '2025-06-25 17:30:00'),
(2, 12, 80.75, 'IN_PROGRESS', '2025-07-01 11:00:00', NULL, '2025-08-01 13:15:00'),
(2, 13, 65.40, 'IN_PROGRESS', '2025-07-15 14:30:00', NULL, '2025-08-01 10:20:00'),
(2, 15, 100.00, 'COMPLETED', '2025-06-05 09:30:00', '2025-06-18 16:45:00', '2025-06-18 16:45:00'),
(2, 19, 55.20, 'IN_PROGRESS', '2025-07-22 13:00:00', NULL, '2025-08-01 12:30:00'),
(2, 41, 70.30, 'IN_PROGRESS', '2025-07-18 10:15:00', NULL, '2025-08-01 14:45:00'),

-- 其他学生的学习记录
(8, 1, 40.50, 'IN_PROGRESS', '2025-07-20 09:00:00', NULL, '2025-08-01 16:00:00'),
(8, 2, 25.30, 'IN_PROGRESS', '2025-07-25 10:30:00', NULL, '2025-08-01 11:20:00'),
(8, 11, 85.60, 'IN_PROGRESS', '2025-07-12 14:00:00', NULL, '2025-08-01 15:30:00'),
(8, 31, 60.40, 'IN_PROGRESS', '2025-07-18 11:30:00', NULL, '2025-08-01 13:45:00'),

(9, 4, 90.80, 'IN_PROGRESS', '2025-07-08 08:30:00', NULL, '2025-08-01 17:15:00'),
(9, 12, 100.00, 'COMPLETED', '2025-06-15 09:00:00', '2025-07-05 18:30:00', '2025-07-05 18:30:00'),
(9, 13, 75.50, 'IN_PROGRESS', '2025-07-10 13:30:00', NULL, '2025-08-01 12:00:00'),
(9, 15, 50.20, 'IN_PROGRESS', '2025-07-22 15:00:00', NULL, '2025-08-01 14:30:00'),

(10, 1, 100.00, 'COMPLETED', '2025-06-25 10:00:00', '2025-07-12 17:45:00', '2025-07-12 17:45:00'),
(10, 3, 80.70, 'IN_PROGRESS', '2025-07-15 11:30:00', NULL, '2025-08-01 16:20:00'),
(10, 21, 35.90, 'IN_PROGRESS', '2025-07-28 09:15:00', NULL, '2025-08-01 10:45:00'),
(10, 42, 45.60, 'IN_PROGRESS', '2025-07-20 14:45:00', NULL, '2025-08-01 13:30:00');

-- 插入用户行为数据 (推荐算法用)
INSERT INTO user_behaviors (user_id, course_id, behavior_type, behavior_value, created_at) VALUES
-- admin用户 (ID=1) 的行为数据
(1, 1, 'VIEW', NULL, '2025-07-01 08:30:00'),
(1, 1, 'ENROLL', NULL, '2025-07-01 09:00:00'),
(1, 1, 'COMPLETE', NULL, '2025-07-15 18:30:00'),
(1, 1, 'RATE', 4.8, '2025-07-15 19:00:00'),
(1, 1, 'FEEDBACK', NULL, '2025-07-15 19:15:00'),

(1, 2, 'VIEW', NULL, '2025-07-15 20:00:00'),
(1, 2, 'ENROLL', NULL, '2025-07-16 10:00:00'),
(1, 2, 'RATE', 4.5, '2025-08-01 14:30:00'),

(1, 3, 'VIEW', NULL, '2025-06-14 19:30:00'),
(1, 3, 'ENROLL', NULL, '2025-06-15 08:30:00'),
(1, 3, 'COMPLETE', NULL, '2025-06-30 16:45:00'),
(1, 3, 'RATE', 4.9, '2025-06-30 17:00:00'),

(1, 11, 'VIEW', NULL, '2025-07-19 18:00:00'),
(1, 11, 'ENROLL', NULL, '2025-07-20 11:00:00'),
(1, 11, 'RATE', 4.3, '2025-08-01 09:30:00'),

(1, 12, 'VIEW', NULL, '2025-07-24 20:30:00'),
(1, 12, 'ENROLL', NULL, '2025-07-25 13:30:00'),

(1, 21, 'VIEW', NULL, '2025-07-27 19:45:00'),
(1, 21, 'ENROLL', NULL, '2025-07-28 14:00:00'),

(1, 31, 'VIEW', NULL, '2025-05-31 21:00:00'),
(1, 31, 'ENROLL', NULL, '2025-06-01 10:00:00'),
(1, 31, 'COMPLETE', NULL, '2025-06-20 17:00:00'),
(1, 31, 'RATE', 4.7, '2025-06-20 17:30:00'),

(1, 41, 'VIEW', NULL, '2025-07-09 20:15:00'),
(1, 41, 'ENROLL', NULL, '2025-07-10 09:30:00'),
(1, 41, 'RATE', 4.6, '2025-08-01 12:00:00'),

-- 浏览但未报名的课程
(1, 4, 'VIEW', NULL, '2025-07-20 19:00:00'),
(1, 5, 'VIEW', NULL, '2025-07-22 20:30:00'),
(1, 13, 'VIEW', NULL, '2025-07-25 18:45:00'),
(1, 14, 'VIEW', NULL, '2025-07-28 19:30:00'),
(1, 22, 'VIEW', NULL, '2025-07-30 20:00:00'),
(1, 32, 'VIEW', NULL, '2025-08-01 19:15:00'),
(1, 42, 'VIEW', NULL, '2025-08-01 20:45:00'),
(1, 43, 'VIEW', NULL, '2025-08-01 21:00:00'),

-- user用户 (ID=2) 的行为数据
(2, 1, 'VIEW', NULL, '2025-07-04 19:30:00'),
(2, 1, 'ENROLL', NULL, '2025-07-05 10:30:00'),
(2, 1, 'RATE', 4.4, '2025-08-01 15:30:00'),

(2, 4, 'VIEW', NULL, '2025-06-19 18:00:00'),
(2, 4, 'ENROLL', NULL, '2025-06-20 09:00:00'),
(2, 4, 'COMPLETE', NULL, '2025-07-10 18:00:00'),
(2, 4, 'RATE', 4.7, '2025-07-10 18:30:00'),
(2, 4, 'FEEDBACK', NULL, '2025-07-10 19:00:00'),

(2, 11, 'VIEW', NULL, '2025-06-09 20:00:00'),
(2, 11, 'ENROLL', NULL, '2025-06-10 08:00:00'),
(2, 11, 'COMPLETE', NULL, '2025-06-25 17:30:00'),
(2, 11, 'RATE', 4.8, '2025-06-25 18:00:00'),

(2, 12, 'VIEW', NULL, '2025-06-30 19:30:00'),
(2, 12, 'ENROLL', NULL, '2025-07-01 11:00:00'),
(2, 12, 'RATE', 4.5, '2025-08-01 13:30:00'),

(2, 13, 'VIEW', NULL, '2025-07-14 18:45:00'),
(2, 13, 'ENROLL', NULL, '2025-07-15 14:30:00'),

(2, 15, 'VIEW', NULL, '2025-06-04 19:15:00'),
(2, 15, 'ENROLL', NULL, '2025-06-05 09:30:00'),
(2, 15, 'COMPLETE', NULL, '2025-06-18 16:45:00'),
(2, 15, 'RATE', 4.9, '2025-06-18 17:15:00'),

(2, 19, 'VIEW', NULL, '2025-07-21 20:30:00'),
(2, 19, 'ENROLL', NULL, '2025-07-22 13:00:00'),

(2, 41, 'VIEW', NULL, '2025-07-17 19:45:00'),
(2, 41, 'ENROLL', NULL, '2025-07-18 10:15:00'),
(2, 41, 'RATE', 4.4, '2025-08-01 15:00:00'),

-- 浏览但未报名的课程
(2, 2, 'VIEW', NULL, '2025-07-18 19:00:00'),
(2, 3, 'VIEW', NULL, '2025-07-20 20:15:00'),
(2, 14, 'VIEW', NULL, '2025-07-23 18:30:00'),
(2, 16, 'VIEW', NULL, '2025-07-25 19:45:00'),
(2, 21, 'VIEW', NULL, '2025-07-28 20:30:00'),
(2, 31, 'VIEW', NULL, '2025-07-30 19:15:00'),
(2, 42, 'VIEW', NULL, '2025-08-01 20:00:00'),
(2, 44, 'VIEW', NULL, '2025-08-01 21:30:00'),

-- 其他学生的行为数据
-- student1 (ID=8)
(8, 1, 'VIEW', NULL, '2025-07-19 18:30:00'),
(8, 1, 'ENROLL', NULL, '2025-07-20 09:00:00'),
(8, 2, 'VIEW', NULL, '2025-07-24 19:15:00'),
(8, 2, 'ENROLL', NULL, '2025-07-25 10:30:00'),
(8, 11, 'VIEW', NULL, '2025-07-11 20:00:00'),
(8, 11, 'ENROLL', NULL, '2025-07-12 14:00:00'),
(8, 31, 'VIEW', NULL, '2025-07-17 19:30:00'),
(8, 31, 'ENROLL', NULL, '2025-07-18 11:30:00'),
(8, 4, 'VIEW', NULL, '2025-07-26 20:15:00'),
(8, 12, 'VIEW', NULL, '2025-07-28 19:45:00'),
(8, 21, 'VIEW', NULL, '2025-07-30 18:30:00'),

-- student2 (ID=9)
(9, 4, 'VIEW', NULL, '2025-07-07 19:00:00'),
(9, 4, 'ENROLL', NULL, '2025-07-08 08:30:00'),
(9, 12, 'VIEW', NULL, '2025-06-14 18:45:00'),
(9, 12, 'ENROLL', NULL, '2025-06-15 09:00:00'),
(9, 12, 'COMPLETE', NULL, '2025-07-05 18:30:00'),
(9, 12, 'RATE', 4.6, '2025-07-05 19:00:00'),
(9, 13, 'VIEW', NULL, '2025-07-09 20:30:00'),
(9, 13, 'ENROLL', NULL, '2025-07-10 13:30:00'),
(9, 15, 'VIEW', NULL, '2025-07-21 19:15:00'),
(9, 15, 'ENROLL', NULL, '2025-07-22 15:00:00'),
(9, 11, 'VIEW', NULL, '2025-07-25 20:00:00'),
(9, 14, 'VIEW', NULL, '2025-07-27 18:45:00'),
(9, 16, 'VIEW', NULL, '2025-07-29 19:30:00'),

-- student3 (ID=10)
(10, 1, 'VIEW', NULL, '2025-06-24 19:30:00'),
(10, 1, 'ENROLL', NULL, '2025-06-25 10:00:00'),
(10, 1, 'COMPLETE', NULL, '2025-07-12 17:45:00'),
(10, 1, 'RATE', 4.5, '2025-07-12 18:15:00'),
(10, 3, 'VIEW', NULL, '2025-07-14 20:00:00'),
(10, 3, 'ENROLL', NULL, '2025-07-15 11:30:00'),
(10, 21, 'VIEW', NULL, '2025-07-27 19:15:00'),
(10, 21, 'ENROLL', NULL, '2025-07-28 09:15:00'),
(10, 42, 'VIEW', NULL, '2025-07-19 18:30:00'),
(10, 42, 'ENROLL', NULL, '2025-07-20 14:45:00'),
(10, 2, 'VIEW', NULL, '2025-07-30 20:15:00'),
(10, 5, 'VIEW', NULL, '2025-08-01 19:00:00'),
(10, 31, 'VIEW', NULL, '2025-08-01 20:30:00');

-- 插入推荐数据
INSERT INTO recommendations (user_id, course_id, algorithm_type, score, reason, created_at) VALUES
-- admin用户 (ID=1) 的推荐 - 基于已学Java、C++、HTML、Spring Boot、MySQL、Python数据分析
(1, 4, 'COLLABORATIVE', 0.8500, '基于相似用户的学习偏好推荐', '2025-08-02 10:00:00'),
(1, 5, 'CONTENT_BASED', 0.9200, '基于您对编程课程的兴趣', '2025-08-02 10:00:00'),
(1, 6, 'HYBRID', 0.8900, '结合协同过滤和内容分析的推荐', '2025-08-02 10:00:00'),
(1, 7, 'COLLABORATIVE', 0.7800, '学习了Java基础的用户也喜欢移动开发', '2025-08-02 10:00:00'),
(1, 8, 'CONTENT_BASED', 0.8300, '与您已学课程内容相关', '2025-08-02 10:00:00'),
(1, 9, 'COLLABORATIVE', 0.7600, '相似背景用户的热门选择', '2025-08-02 10:00:00'),
(1, 10, 'HYBRID', 0.8100, '综合推荐算法建议', '2025-08-02 10:00:00'),
(1, 13, 'CONTENT_BASED', 0.8700, '基于您的前端学习兴趣', '2025-08-02 10:00:00'),
(1, 14, 'COLLABORATIVE', 0.7400, '基于用户群体偏好', '2025-08-02 10:00:00'),
(1, 16, 'CONTENT_BASED', 0.7900, '技术栈相关推荐', '2025-08-02 10:00:00'),
(1, 22, 'HYBRID', 0.8600, '个性化混合推荐', '2025-08-02 10:00:00'),
(1, 23, 'COLLABORATIVE', 0.7200, '相似用户喜欢的课程', '2025-08-02 10:00:00'),
(1, 24, 'CONTENT_BASED', 0.7700, '基于学习历史的内容推荐', '2025-08-02 10:00:00'),
(1, 32, 'HYBRID', 0.8400, '数据库进阶推荐', '2025-08-02 10:00:00'),
(1, 42, 'CONTENT_BASED', 0.9100, '基于您的数据分析兴趣', '2025-08-02 10:00:00'),
(1, 43, 'HYBRID', 0.8800, 'AI领域深度推荐', '2025-08-02 10:00:00'),
(1, 46, 'COLLABORATIVE', 0.8000, '数据科学相关推荐', '2025-08-02 10:00:00'),
(1, 47, 'CONTENT_BASED', 0.7500, '大数据技术推荐', '2025-08-02 10:00:00'),

-- user用户 (ID=2) 的推荐 - 基于已学JavaScript、HTML、Vue.js、React、TypeScript、Node.js、Python数据分析
(2, 2, 'CONTENT_BASED', 0.8800, '基于您的编程基础推荐', '2025-08-02 10:00:00'),
(2, 3, 'COLLABORATIVE', 0.7900, '相似用户的学习路径', '2025-08-02 10:00:00'),
(2, 5, 'HYBRID', 0.8200, '后端技术栈推荐', '2025-08-02 10:00:00'),
(2, 14, 'CONTENT_BASED', 0.9000, '基于您的前端框架兴趣', '2025-08-02 10:00:00'),
(2, 16, 'COLLABORATIVE', 0.8500, '前端工具链推荐', '2025-08-02 10:00:00'),
(2, 17, 'CONTENT_BASED', 0.7600, 'CSS预处理器推荐', '2025-08-02 10:00:00'),
(2, 18, 'HYBRID', 0.7300, 'UI框架推荐', '2025-08-02 10:00:00'),
(2, 20, 'COLLABORATIVE', 0.8100, '桌面应用开发推荐', '2025-08-02 10:00:00'),
(2, 21, 'CONTENT_BASED', 0.8700, '基于您的Node.js兴趣', '2025-08-02 10:00:00'),
(2, 23, 'HYBRID', 0.8400, 'API开发推荐', '2025-08-02 10:00:00'),
(2, 26, 'COLLABORATIVE', 0.7800, 'Python Web框架推荐', '2025-08-02 10:00:00'),
(2, 29, 'CONTENT_BASED', 0.7500, '轻量级框架推荐', '2025-08-02 10:00:00'),
(2, 30, 'HYBRID', 0.8600, '企业级框架推荐', '2025-08-02 10:00:00'),
(2, 31, 'COLLABORATIVE', 0.7400, '数据库技术推荐', '2025-08-02 10:00:00'),
(2, 42, 'CONTENT_BASED', 0.8300, '机器学习入门推荐', '2025-08-02 10:00:00'),
(2, 44, 'HYBRID', 0.7700, 'NLP技术推荐', '2025-08-02 10:00:00'),
(2, 46, 'COLLABORATIVE', 0.7200, '数据挖掘推荐', '2025-08-02 10:00:00'),
(2, 49, 'CONTENT_BASED', 0.7000, 'R语言推荐', '2025-08-02 10:00:00'),

-- student1 (ID=8) 的推荐 - 基于已学Java、Python、HTML、MySQL
(8, 3, 'CONTENT_BASED', 0.8500, '基于您的编程基础推荐', '2025-08-02 10:00:00'),
(8, 4, 'COLLABORATIVE', 0.7800, '相似学习路径推荐', '2025-08-02 10:00:00'),
(8, 5, 'HYBRID', 0.8200, '后端开发推荐', '2025-08-02 10:00:00'),
(8, 12, 'CONTENT_BASED', 0.8700, '前端框架推荐', '2025-08-02 10:00:00'),
(8, 21, 'COLLABORATIVE', 0.8400, 'Spring Boot推荐', '2025-08-02 10:00:00'),
(8, 32, 'HYBRID', 0.7900, '数据库进阶推荐', '2025-08-02 10:00:00'),
(8, 41, 'CONTENT_BASED', 0.8600, '数据分析推荐', '2025-08-02 10:00:00'),
(8, 42, 'COLLABORATIVE', 0.8000, '机器学习推荐', '2025-08-02 10:00:00'),

-- student2 (ID=9) 的推荐 - 基于已学JavaScript、Vue.js、React、TypeScript
(9, 1, 'CONTENT_BASED', 0.8300, '编程基础推荐', '2025-08-02 10:00:00'),
(9, 11, 'COLLABORATIVE', 0.8600, 'HTML基础推荐', '2025-08-02 10:00:00'),
(9, 14, 'CONTENT_BASED', 0.9100, '前端框架进阶推荐', '2025-08-02 10:00:00'),
(9, 16, 'HYBRID', 0.8500, '构建工具推荐', '2025-08-02 10:00:00'),
(9, 19, 'COLLABORATIVE', 0.8200, 'Node.js推荐', '2025-08-02 10:00:00'),
(9, 20, 'CONTENT_BASED', 0.7800, '桌面应用推荐', '2025-08-02 10:00:00'),
(9, 23, 'HYBRID', 0.8000, 'API开发推荐', '2025-08-02 10:00:00'),
(9, 30, 'COLLABORATIVE', 0.7700, '企业级框架推荐', '2025-08-02 10:00:00'),

-- student3 (ID=10) 的推荐 - 基于已学Java、C++、Spring Boot、机器学习
(10, 2, 'CONTENT_BASED', 0.8400, 'Python编程推荐', '2025-08-02 10:00:00'),
(10, 5, 'COLLABORATIVE', 0.8100, 'Go语言推荐', '2025-08-02 10:00:00'),
(10, 6, 'HYBRID', 0.8700, 'Rust系统编程推荐', '2025-08-02 10:00:00'),
(10, 22, 'CONTENT_BASED', 0.8300, 'Django框架推荐', '2025-08-02 10:00:00'),
(10, 24, 'COLLABORATIVE', 0.7900, '.NET开发推荐', '2025-08-02 10:00:00'),
(10, 31, 'HYBRID', 0.8500, 'MySQL进阶推荐', '2025-08-02 10:00:00'),
(10, 41, 'CONTENT_BASED', 0.8800, '数据分析推荐', '2025-08-02 10:00:00'),
(10, 43, 'COLLABORATIVE', 0.9000, '深度学习推荐', '2025-08-02 10:00:00'),
(10, 45, 'HYBRID', 0.8600, '计算机视觉推荐', '2025-08-02 10:00:00'),
(10, 47, 'CONTENT_BASED', 0.8200, '大数据处理推荐', '2025-08-02 10:00:00'),
(10, 50, 'COLLABORATIVE', 0.8400, '强化学习推荐', '2025-08-02 10:00:00');

-- 显示统计信息
SELECT '=== 数据库初始化完成！ ===' as message;
SELECT CONCAT('用户总数: ', COUNT(*)) as user_count FROM users WHERE deleted = 0;
SELECT CONCAT('课程总数: ', COUNT(*)) as course_count FROM courses WHERE deleted = 0;
SELECT CONCAT('学习记录总数: ', COUNT(*)) as learning_record_count FROM learning_records WHERE deleted = 0;
SELECT CONCAT('用户行为总数: ', COUNT(*)) as behavior_count FROM user_behaviors WHERE deleted = 0;
SELECT CONCAT('推荐记录总数: ', COUNT(*)) as recommendation_count FROM recommendations WHERE deleted = 0;

-- 显示测试账号信息
SELECT '=== 测试账号信息 ===' as info;
SELECT username, email, role, 'admin123' as password_info FROM users WHERE username = 'admin';
SELECT username, email, role, 'user123' as password_info FROM users WHERE username = 'user';

-- 显示课程分类统计
SELECT '=== 课程分类统计 ===' as category_info;
SELECT category, COUNT(*) as course_count FROM courses WHERE deleted = 0 GROUP BY category ORDER BY course_count DESC;

-- 显示推荐算法统计
SELECT '=== 推荐算法统计 ===' as recommendation_info;
SELECT algorithm_type, COUNT(*) as recommendation_count FROM recommendations WHERE deleted = 0 GROUP BY algorithm_type;

-- 显示用户学习统计
SELECT '=== 用户学习统计 ===' as learning_info;
SELECT u.username, COUNT(lr.id) as enrolled_courses,
       AVG(lr.progress) as avg_progress,
       COUNT(CASE WHEN lr.status = 'COMPLETED' THEN 1 END) as completed_courses
FROM users u
LEFT JOIN learning_records lr ON u.id = lr.user_id AND lr.deleted = 0
WHERE u.deleted = 0 AND u.role = 'STUDENT'
GROUP BY u.id, u.username
ORDER BY enrolled_courses DESC;
