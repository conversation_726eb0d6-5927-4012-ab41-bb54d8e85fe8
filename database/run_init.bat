@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 学习平台数据库初始化脚本执行器 (Windows版本)
REM 使用方法: run_init.bat

REM MySQL配置
set MYSQL_HOST=***************
set MYSQL_PORT=3306
set MYSQL_USER=root
set MYSQL_PASSWORD=mysql
set DATABASE_NAME=learning_platform
set SQL_FILE=init_learning_platform.sql

REM 颜色定义
set "RED=[31m"
set "GREEN=[32m"
set "YELLOW=[33m"
set "BLUE=[34m"
set "NC=[0m"

echo %BLUE%=== 学习平台数据库初始化 ===%NC%
echo %YELLOW%数据库服务器: %MYSQL_HOST%:%MYSQL_PORT%%NC%
echo %YELLOW%数据库: %DATABASE_NAME%%NC%
echo %YELLOW%用户: %MYSQL_USER%%NC%
echo.

REM 检查MySQL客户端是否可用
where mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%错误: 未找到mysql命令，请确保MySQL客户端已安装并添加到PATH环境变量%NC%
    echo %RED%或者安装MySQL Workbench、phpMyAdmin等工具手动执行SQL脚本%NC%
    pause
    exit /b 1
)

REM 检查MySQL连接
echo %BLUE%检查MySQL连接...%NC%
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USER% -p%MYSQL_PASSWORD% -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%错误: 无法连接到MySQL服务器%NC%
    echo %RED%请检查以下配置:%NC%
    echo %RED%- MySQL服务器地址: %MYSQL_HOST%:%MYSQL_PORT%%NC%
    echo %RED%- 用户名: %MYSQL_USER%%NC%
    echo %RED%- 密码: %MYSQL_PASSWORD%%NC%
    echo %RED%- 网络连接是否正常%NC%
    pause
    exit /b 1
)
echo %GREEN%MySQL连接成功！%NC%

REM 检查SQL文件是否存在
if not exist "%SQL_FILE%" (
    echo %RED%错误: SQL文件 %SQL_FILE% 不存在%NC%
    pause
    exit /b 1
)

REM 执行SQL文件
echo %BLUE%开始执行数据库初始化...%NC%
echo %YELLOW%这将删除现有的 %DATABASE_NAME% 数据库并重新创建%NC%
set /p confirm="确认继续? (y/N): "
if /i not "%confirm%"=="y" (
    echo %YELLOW%操作已取消%NC%
    pause
    exit /b 0
)

echo %BLUE%执行SQL脚本...%NC%
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USER% -p%MYSQL_PASSWORD% < %SQL_FILE%

if %errorlevel% equ 0 (
    echo %GREEN%=== 数据库初始化完成！ ===%NC%
    echo.
    echo %GREEN%测试账号信息:%NC%
    echo %YELLOW%管理员账号: admin / admin%NC%
    echo %YELLOW%普通用户账号: user / user%NC%
    echo %YELLOW%测试账号: 123 / 123123%NC%
    echo.
    echo %GREEN%数据统计:%NC%
    echo %YELLOW%- 20门课程%NC%
    echo %YELLOW%- 10个用户%NC%
    echo %YELLOW%- 丰富的学习记录和用户行为数据%NC%
    echo %YELLOW%- 预生成的推荐数据%NC%
    echo.
    echo %GREEN%现在可以启动应用程序进行测试！%NC%
) else (
    echo %RED%数据库初始化失败！%NC%
    echo %RED%请检查SQL文件内容和数据库权限%NC%
)

pause
