-- 学习平台完整数据库初始化脚本
-- 包含所有微服务的表结构和测试数据

DROP DATABASE IF EXISTS learning_platform;
CREATE DATABASE learning_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE learning_platform;

-- 创建用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    role ENUM('ADMIN', 'TEACHER', 'STUDENT') DEFAULT 'STUDENT',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE',
    avatar_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
);

-- 创建课程分类表
CREATE TABLE course_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_parent (parent_id)
);

-- 创建课程表
CREATE TABLE courses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    content TEXT,
    teacher_id BIGINT NOT NULL,
    category_id BIGINT,
    difficulty_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED') DEFAULT 'BEGINNER',
    cover_image VARCHAR(255),
    duration_hours INT DEFAULT 0,
    price DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'PUBLISHED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_category (category_id),
    INDEX idx_teacher (teacher_id),
    INDEX idx_status (status),
    FOREIGN KEY (category_id) REFERENCES course_categories(id),
    FOREIGN KEY (teacher_id) REFERENCES users(id)
);

-- 创建课程章节表
CREATE TABLE course_chapters (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    course_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content_type VARCHAR(20) DEFAULT 'VIDEO',
    content_url VARCHAR(500),
    duration_minutes INT DEFAULT 0,
    order_index INT DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_course (course_id),
    INDEX idx_order (order_index),
    FOREIGN KEY (course_id) REFERENCES courses(id)
);

-- 创建课程注册表
CREATE TABLE course_enrollments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    deleted TINYINT(1) DEFAULT 0,
    CONSTRAINT unique_enrollment UNIQUE (user_id, course_id),
    INDEX idx_user (user_id),
    INDEX idx_course (course_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (course_id) REFERENCES courses(id)
);

-- 创建学习记录表
CREATE TABLE learning_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    progress DECIMAL(5,2) DEFAULT 0.00,
    status ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'PAUSED') DEFAULT 'NOT_STARTED',
    start_time TIMESTAMP NULL,
    completion_time TIMESTAMP NULL,
    last_access_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    UNIQUE KEY uk_user_course (user_id, course_id),
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_status (status)
);

-- 创建学习进度表
CREATE TABLE learning_progress (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    chapter_id BIGINT NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    watch_duration_seconds INT DEFAULT 0,
    last_position_seconds INT DEFAULT 0,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user_course (user_id, course_id),
    INDEX idx_chapter (chapter_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (course_id) REFERENCES courses(id),
    FOREIGN KEY (chapter_id) REFERENCES course_chapters(id)
);

-- 创建测验记录表
CREATE TABLE quiz_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    chapter_id BIGINT NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    total_score DECIMAL(5,2) NOT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user (user_id),
    INDEX idx_chapter (chapter_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (chapter_id) REFERENCES course_chapters(id)
);

-- 创建讨论主题表
CREATE TABLE discussion_topics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    course_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    view_count INT DEFAULT 0,
    reply_count INT DEFAULT 0,
    last_reply_at TIMESTAMP NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_locked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_course (course_id),
    INDEX idx_user (user_id),
    INDEX idx_created (created_at),
    FOREIGN KEY (course_id) REFERENCES courses(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建讨论回复表
CREATE TABLE discussion_replies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    topic_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    parent_id BIGINT NULL,
    like_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_topic (topic_id),
    INDEX idx_user (user_id),
    INDEX idx_parent (parent_id),
    INDEX idx_created (created_at),
    FOREIGN KEY (topic_id) REFERENCES discussion_topics(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (parent_id) REFERENCES discussion_replies(id)
);

-- 创建用户行为表 (推荐算法用)
CREATE TABLE user_behaviors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    behavior_type ENUM('VIEW', 'ENROLL', 'COMPLETE', 'RATE', 'FEEDBACK') NOT NULL,
    behavior_value DECIMAL(3,1) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_created_at (created_at)
);

-- 创建推荐表
CREATE TABLE recommendations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    algorithm_type ENUM('COLLABORATIVE', 'CONTENT_BASED', 'HYBRID') NOT NULL,
    score DECIMAL(4,3) NOT NULL,
    reason VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_algorithm_type (algorithm_type),
    INDEX idx_score (score),
    INDEX idx_created_at (created_at)
);

-- 插入课程分类数据
INSERT INTO course_categories (id, name, description, parent_id, created_at, updated_at, deleted) VALUES
(1, '编程开发', '编程和软件开发相关课程', NULL, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(2, '前端开发', '前端技术和框架相关课程', 1, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(3, '后端开发', '后端技术和框架相关课程', 1, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(4, '数据库技术', '数据库设计和管理相关课程', 1, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(5, '人工智能', 'AI和机器学习相关课程', NULL, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0),
(6, '数据科学', '数据分析和数据科学相关课程', 5, '2025-07-01 10:00:00', '2025-07-01 10:00:00', 0);

-- 插入用户数据 (保留现有密码哈希)
INSERT INTO users (id, username, email, password, nickname, role, status) VALUES
(1, 'admin', '<EMAIL>', '$2a$10$.uchxSCoSEnCH6r3pI9Ln.KQpjlQFnGS2CpqwdkRVKSTOPmJlw3Ri', '管理员', 'ADMIN', 'ACTIVE'),
(2, 'user', '<EMAIL>', '$2a$10$JJ4g2POg7wfohtmazav7huTGTSow5vfGFG/wvahXVND1U3tXw5fX.', '普通用户', 'STUDENT', 'ACTIVE'),
(3, 'teacher1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '张教授', 'TEACHER', 'ACTIVE'),
(4, 'teacher2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '李老师', 'TEACHER', 'ACTIVE'),
(5, 'teacher3', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '王老师', 'TEACHER', 'ACTIVE'),
(6, 'teacher4', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '赵老师', 'TEACHER', 'ACTIVE'),
(7, 'teacher5', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '刘老师', 'TEACHER', 'ACTIVE'),
(8, 'student1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小明', 'STUDENT', 'ACTIVE'),
(9, 'student2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小红', 'STUDENT', 'ACTIVE'),
(10, 'student3', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxp.nMRZ.2Y7Azq', '小李', 'STUDENT', 'ACTIVE');

-- 插入50个课程数据
INSERT INTO courses (id, title, description, content, teacher_id, category_id, difficulty_level, duration_hours, price, status) VALUES
-- 编程基础类 (1-10)
(1, 'Java编程基础', 'Java编程语言基础课程，从零开始学习Java', 'Java语法、面向对象编程、集合框架、异常处理等基础知识', 3, 1, 'BEGINNER', 40, 199.00, 'PUBLISHED'),
(2, 'Python入门到精通', 'Python编程语言完整学习路径', 'Python语法、数据结构、函数、模块、面向对象编程', 4, 1, 'BEGINNER', 45, 179.00, 'PUBLISHED'),
(3, 'C++程序设计', 'C++编程语言系统学习', 'C++语法、指针、类与对象、STL标准库', 5, 1, 'INTERMEDIATE', 50, 249.00, 'PUBLISHED'),
(4, 'JavaScript现代开发', 'JavaScript ES6+现代开发技术', 'ES6语法、异步编程、模块化、函数式编程', 3, 1, 'INTERMEDIATE', 35, 219.00, 'PUBLISHED'),
(5, 'Go语言实战', 'Go语言从入门到实战项目', 'Go语法、并发编程、网络编程、微服务开发', 6, 1, 'INTERMEDIATE', 42, 289.00, 'PUBLISHED'),
(6, 'Rust系统编程', 'Rust语言系统级编程', 'Rust语法、内存管理、并发安全、系统编程', 7, 1, 'ADVANCED', 48, 329.00, 'PUBLISHED'),
(7, 'Kotlin移动开发', 'Kotlin语言Android开发', 'Kotlin语法、Android开发、UI设计、数据存储', 4, 1, 'INTERMEDIATE', 38, 259.00, 'PUBLISHED'),
(8, 'Swift iOS开发', 'Swift语言iOS应用开发', 'Swift语法、iOS开发、UIKit、SwiftUI', 5, 1, 'INTERMEDIATE', 44, 279.00, 'PUBLISHED'),
(9, 'PHP Web开发', 'PHP服务器端开发技术', 'PHP语法、MySQL数据库、Laravel框架', 6, 1, 'BEGINNER', 36, 189.00, 'PUBLISHED'),
(10, 'Ruby on Rails', 'Ruby语言Web开发框架', 'Ruby语法、Rails框架、MVC架构、RESTful API', 7, 1, 'INTERMEDIATE', 40, 269.00, 'PUBLISHED'),

-- 前端开发类 (11-20)
(11, 'HTML5+CSS3基础', 'Web前端基础技术学习', 'HTML5语义化、CSS3样式、响应式设计、Flexbox布局', 3, 2, 'BEGINNER', 30, 149.00, 'PUBLISHED'),
(12, 'Vue.js 3.0实战', 'Vue.js现代前端框架开发', 'Vue3组合式API、路由管理、状态管理、组件开发', 4, 2, 'INTERMEDIATE', 45, 299.00, 'PUBLISHED'),
(13, 'React开发进阶', 'React前端框架深度学习', 'React Hooks、状态管理、性能优化、测试', 5, 2, 'INTERMEDIATE', 48, 319.00, 'PUBLISHED'),
(14, 'Angular企业级开发', 'Angular框架企业应用开发', 'Angular架构、依赖注入、RxJS、企业级项目', 6, 2, 'ADVANCED', 52, 359.00, 'PUBLISHED'),
(15, 'TypeScript高级编程', 'TypeScript静态类型编程', 'TypeScript语法、类型系统、装饰器、高级特性', 3, 2, 'INTERMEDIATE', 35, 239.00, 'PUBLISHED'),
(16, 'Webpack构建工具', '现代前端构建工具学习', 'Webpack配置、模块打包、性能优化、插件开发', 4, 2, 'INTERMEDIATE', 28, 199.00, 'PUBLISHED'),
(17, 'Sass/Less预处理器', 'CSS预处理器技术', 'Sass语法、Less语法、模块化CSS、构建集成', 5, 2, 'BEGINNER', 20, 129.00, 'PUBLISHED'),
(18, 'Bootstrap响应式设计', 'Bootstrap前端UI框架', 'Bootstrap组件、栅格系统、响应式设计、定制化', 6, 2, 'BEGINNER', 25, 159.00, 'PUBLISHED'),
(19, 'Node.js全栈开发', 'Node.js服务器端JavaScript', 'Node.js基础、Express框架、数据库集成、API开发', 7, 2, 'INTERMEDIATE', 42, 289.00, 'PUBLISHED'),
(20, 'Electron桌面应用', 'Electron跨平台桌面应用开发', 'Electron架构、主进程、渲染进程、原生API', 3, 2, 'ADVANCED', 38, 279.00, 'PUBLISHED'),

-- 后端开发类 (21-30)
(21, 'Spring Boot微服务', 'Spring Boot企业级开发', 'Spring Boot核心、微服务架构、云原生开发', 4, 3, 'INTERMEDIATE', 55, 349.00, 'PUBLISHED'),
(22, 'Django Web框架', 'Python Django Web开发', 'Django架构、ORM、模板系统、RESTful API', 5, 3, 'INTERMEDIATE', 48, 299.00, 'PUBLISHED'),
(23, 'Express.js API开发', 'Node.js Express框架开发', 'Express路由、中间件、数据库集成、API设计', 6, 3, 'INTERMEDIATE', 35, 229.00, 'PUBLISHED'),
(24, 'ASP.NET Core开发', '.NET Core跨平台开发', '.NET Core架构、Web API、Entity Framework、部署', 7, 3, 'INTERMEDIATE', 50, 329.00, 'PUBLISHED'),
(25, 'Rails API开发', 'Ruby on Rails API开发', 'Rails API模式、JSON序列化、认证授权、测试', 3, 3, 'INTERMEDIATE', 40, 279.00, 'PUBLISHED'),
(26, 'FastAPI Python框架', 'FastAPI现代Python Web框架', 'FastAPI特性、异步编程、API文档、性能优化', 4, 3, 'INTERMEDIATE', 32, 249.00, 'PUBLISHED'),
(27, 'Gin Go Web框架', 'Gin轻量级Go Web框架', 'Gin路由、中间件、JSON处理、性能调优', 5, 3, 'INTERMEDIATE', 28, 219.00, 'PUBLISHED'),
(28, 'Laravel PHP框架', 'Laravel优雅的PHP框架', 'Laravel架构、Eloquent ORM、Blade模板、队列', 6, 3, 'INTERMEDIATE', 45, 299.00, 'PUBLISHED'),
(29, 'Koa.js轻量框架', 'Koa.js下一代Node.js框架', 'Koa中间件、async/await、错误处理、性能', 7, 3, 'INTERMEDIATE', 25, 199.00, 'PUBLISHED'),
(30, 'Nest.js企业框架', 'Nest.js企业级Node.js框架', 'Nest.js架构、依赖注入、装饰器、微服务', 3, 3, 'ADVANCED', 48, 359.00, 'PUBLISHED'),

-- 数据库技术类 (31-40)
(31, 'MySQL数据库设计', 'MySQL数据库设计与优化', 'MySQL架构、索引优化、查询优化、性能调优', 4, 4, 'INTERMEDIATE', 38, 259.00, 'PUBLISHED'),
(32, 'PostgreSQL高级应用', 'PostgreSQL企业级数据库应用', 'PostgreSQL特性、JSON支持、全文搜索、扩展', 5, 4, 'ADVANCED', 42, 299.00, 'PUBLISHED'),
(33, 'MongoDB文档数据库', 'MongoDB NoSQL数据库开发', 'MongoDB架构、文档模型、聚合管道、分片', 6, 4, 'INTERMEDIATE', 35, 229.00, 'PUBLISHED'),
(34, 'Redis缓存技术', 'Redis内存数据库与缓存', 'Redis数据结构、持久化、集群、性能优化', 7, 4, 'INTERMEDIATE', 30, 199.00, 'PUBLISHED'),
(35, 'Elasticsearch搜索', 'Elasticsearch全文搜索引擎', 'ES架构、索引设计、查询DSL、聚合分析', 3, 4, 'ADVANCED', 40, 319.00, 'PUBLISHED'),
(36, 'Oracle数据库管理', 'Oracle企业级数据库管理', 'Oracle架构、PL/SQL、性能调优、备份恢复', 4, 4, 'ADVANCED', 55, 399.00, 'PUBLISHED'),
(37, 'SQLite轻量数据库', 'SQLite嵌入式数据库开发', 'SQLite特性、移动应用集成、性能优化', 5, 4, 'BEGINNER', 20, 129.00, 'PUBLISHED'),
(38, 'InfluxDB时序数据库', 'InfluxDB时间序列数据库', 'InfluxDB架构、时序数据模型、查询语言', 6, 4, 'INTERMEDIATE', 25, 179.00, 'PUBLISHED'),
(39, 'Neo4j图数据库', 'Neo4j图数据库开发', 'Neo4j图模型、Cypher查询、图算法', 7, 4, 'ADVANCED', 35, 279.00, 'PUBLISHED'),
(40, 'Cassandra分布式数据库', 'Cassandra大规模分布式数据库', 'Cassandra架构、数据模型、一致性、扩展性', 3, 4, 'ADVANCED', 45, 349.00, 'PUBLISHED'),

-- AI/数据科学类 (41-50)
(41, 'Python数据分析', 'Python数据分析基础与实战', 'Pandas、NumPy、数据清洗、可视化、统计分析', 4, 6, 'BEGINNER', 40, 299.00, 'PUBLISHED'),
(42, '机器学习入门', '机器学习算法与应用', '监督学习、无监督学习、特征工程、模型评估', 5, 5, 'INTERMEDIATE', 50, 399.00, 'PUBLISHED'),
(43, '深度学习实战', '深度学习框架与应用', 'TensorFlow、PyTorch、神经网络、CNN、RNN', 6, 5, 'ADVANCED', 60, 499.00, 'PUBLISHED'),
(44, '自然语言处理', 'NLP技术与应用', '文本预处理、词向量、情感分析、机器翻译', 7, 5, 'ADVANCED', 45, 399.00, 'PUBLISHED'),
(45, '计算机视觉', '计算机视觉算法与应用', '图像处理、目标检测、人脸识别、图像分类', 3, 5, 'ADVANCED', 48, 429.00, 'PUBLISHED'),
(46, '数据挖掘技术', '数据挖掘算法与实践', '关联规则、聚类分析、分类算法、异常检测', 4, 6, 'INTERMEDIATE', 42, 329.00, 'PUBLISHED'),
(47, '大数据处理', '大数据技术栈学习', 'Hadoop、Spark、Kafka、数据仓库、ETL', 5, 6, 'ADVANCED', 55, 459.00, 'PUBLISHED'),
(48, '统计学基础', '统计学理论与应用', '描述统计、推断统计、假设检验、回归分析', 6, 6, 'BEGINNER', 35, 249.00, 'PUBLISHED'),
(49, 'R语言数据科学', 'R语言数据分析与可视化', 'R语法、ggplot2、dplyr、统计建模', 7, 6, 'INTERMEDIATE', 38, 279.00, 'PUBLISHED'),
(50, '强化学习', '强化学习算法与应用', 'Q-learning、策略梯度、深度强化学习、游戏AI', 3, 5, 'ADVANCED', 48, 369.00, 'PUBLISHED');

-- 插入课程章节数据 (为前10个课程添加章节)
INSERT INTO course_chapters (id, course_id, title, description, content_type, content_url, duration_minutes, order_index, status) VALUES
-- Java编程基础课程章节 (课程ID: 1)
(1, 1, 'Java开发环境搭建', 'JDK安装、IDE配置、第一个Java程序', 'VIDEO', '/videos/java/chapter1.mp4', 45, 1, 'ACTIVE'),
(2, 1, 'Java基础语法', '变量、数据类型、运算符、控制结构', 'VIDEO', '/videos/java/chapter2.mp4', 60, 2, 'ACTIVE'),
(3, 1, '面向对象编程', '类与对象、封装、继承、多态', 'VIDEO', '/videos/java/chapter3.mp4', 75, 3, 'ACTIVE'),
(4, 1, '异常处理', '异常类型、try-catch、自定义异常', 'VIDEO', '/videos/java/chapter4.mp4', 50, 4, 'ACTIVE'),
(5, 1, '集合框架', 'List、Set、Map接口及实现类', 'VIDEO', '/videos/java/chapter5.mp4', 65, 5, 'ACTIVE'),

-- Python入门到精通课程章节 (课程ID: 2)
(6, 2, 'Python环境配置', 'Python安装、虚拟环境、包管理', 'VIDEO', '/videos/python/chapter1.mp4', 40, 1, 'ACTIVE'),
(7, 2, 'Python基础语法', '变量、数据类型、条件语句、循环', 'VIDEO', '/videos/python/chapter2.mp4', 55, 2, 'ACTIVE'),
(8, 2, '函数与模块', '函数定义、参数传递、模块导入', 'VIDEO', '/videos/python/chapter3.mp4', 50, 3, 'ACTIVE'),
(9, 2, '数据结构', '列表、元组、字典、集合操作', 'VIDEO', '/videos/python/chapter4.mp4', 60, 4, 'ACTIVE'),
(10, 2, '面向对象编程', '类定义、继承、多态、特殊方法', 'VIDEO', '/videos/python/chapter5.mp4', 70, 5, 'ACTIVE'),

-- C++程序设计课程章节 (课程ID: 3)
(11, 3, 'C++开发环境', 'C++编译器、IDE配置、项目管理', 'VIDEO', '/videos/cpp/chapter1.mp4', 35, 1, 'ACTIVE'),
(12, 3, 'C++基础语法', '变量、指针、引用、函数重载', 'VIDEO', '/videos/cpp/chapter2.mp4', 65, 2, 'ACTIVE'),
(13, 3, '类与对象', '类定义、构造函数、析构函数', 'VIDEO', '/videos/cpp/chapter3.mp4', 70, 3, 'ACTIVE'),
(14, 3, 'STL标准库', '容器、迭代器、算法、函数对象', 'VIDEO', '/videos/cpp/chapter4.mp4', 80, 4, 'ACTIVE'),
(15, 3, '内存管理', '动态内存分配、智能指针、RAII', 'VIDEO', '/videos/cpp/chapter5.mp4', 75, 5, 'ACTIVE'),

-- JavaScript现代开发课程章节 (课程ID: 4)
(16, 4, 'JavaScript基础', 'ES6语法、变量声明、箭头函数', 'VIDEO', '/videos/js/chapter1.mp4', 45, 1, 'ACTIVE'),
(17, 4, '异步编程', 'Promise、async/await、事件循环', 'VIDEO', '/videos/js/chapter2.mp4', 60, 2, 'ACTIVE'),
(18, 4, '模块化开发', 'ES6模块、CommonJS、模块打包', 'VIDEO', '/videos/js/chapter3.mp4', 50, 3, 'ACTIVE'),
(19, 4, '函数式编程', '高阶函数、闭包、纯函数概念', 'VIDEO', '/videos/js/chapter4.mp4', 55, 4, 'ACTIVE'),
(20, 4, 'DOM操作与事件', 'DOM API、事件处理、性能优化', 'VIDEO', '/videos/js/chapter5.mp4', 65, 5, 'ACTIVE'),

-- Go语言实战课程章节 (课程ID: 5)
(21, 5, 'Go环境搭建', 'Go安装、工作空间、包管理', 'VIDEO', '/videos/go/chapter1.mp4', 30, 1, 'ACTIVE'),
(22, 5, 'Go基础语法', '变量、函数、结构体、接口', 'VIDEO', '/videos/go/chapter2.mp4', 55, 2, 'ACTIVE'),
(23, 5, '并发编程', 'Goroutine、Channel、Select语句', 'VIDEO', '/videos/go/chapter3.mp4', 70, 3, 'ACTIVE'),
(24, 5, '网络编程', 'HTTP服务、TCP/UDP编程', 'VIDEO', '/videos/go/chapter4.mp4', 60, 4, 'ACTIVE'),
(25, 5, '微服务开发', 'gRPC、服务发现、负载均衡', 'VIDEO', '/videos/go/chapter5.mp4', 75, 5, 'ACTIVE'),

-- Vue.js 3.0实战课程章节 (课程ID: 12)
(26, 12, 'Vue3基础入门', 'Vue3安装、项目创建、基础概念', 'VIDEO', '/videos/vue/chapter1.mp4', 40, 1, 'ACTIVE'),
(27, 12, '组合式API', 'setup函数、ref、reactive、computed', 'VIDEO', '/videos/vue/chapter2.mp4', 65, 2, 'ACTIVE'),
(28, 12, '组件开发', '组件通信、插槽、动态组件', 'VIDEO', '/videos/vue/chapter3.mp4', 70, 3, 'ACTIVE'),
(29, 12, '路由管理', 'Vue Router、路由守卫、懒加载', 'VIDEO', '/videos/vue/chapter4.mp4', 55, 4, 'ACTIVE'),
(30, 12, '状态管理', 'Pinia状态管理、模块化状态', 'VIDEO', '/videos/vue/chapter5.mp4', 60, 5, 'ACTIVE'),

-- Spring Boot微服务课程章节 (课程ID: 21)
(31, 21, 'Spring Boot入门', 'Spring Boot项目创建、自动配置', 'VIDEO', '/videos/springboot/chapter1.mp4', 50, 1, 'ACTIVE'),
(32, 21, 'Web开发', 'Controller、RestController、参数绑定', 'VIDEO', '/videos/springboot/chapter2.mp4', 65, 2, 'ACTIVE'),
(33, 21, '数据访问', 'Spring Data JPA、MyBatis集成', 'VIDEO', '/videos/springboot/chapter3.mp4', 70, 3, 'ACTIVE'),
(34, 21, '微服务架构', 'Spring Cloud、服务注册发现', 'VIDEO', '/videos/springboot/chapter4.mp4', 80, 4, 'ACTIVE'),
(35, 21, '部署与监控', 'Docker部署、Actuator监控', 'VIDEO', '/videos/springboot/chapter5.mp4', 60, 5, 'ACTIVE'),

-- MySQL数据库设计课程章节 (课程ID: 31)
(36, 31, 'MySQL基础', 'MySQL安装、基本操作、数据类型', 'VIDEO', '/videos/mysql/chapter1.mp4', 45, 1, 'ACTIVE'),
(37, 31, '数据库设计', '表设计、范式、关系建模', 'VIDEO', '/videos/mysql/chapter2.mp4', 60, 2, 'ACTIVE'),
(38, 31, '索引优化', '索引类型、查询优化、执行计划', 'VIDEO', '/videos/mysql/chapter3.mp4', 70, 3, 'ACTIVE'),
(39, 31, '性能调优', '配置优化、慢查询分析', 'VIDEO', '/videos/mysql/chapter4.mp4', 65, 4, 'ACTIVE'),
(40, 31, '备份恢复', '数据备份、恢复策略、主从复制', 'VIDEO', '/videos/mysql/chapter5.mp4', 55, 5, 'ACTIVE'),

-- Python数据分析课程章节 (课程ID: 41)
(41, 41, '数据分析环境', 'Anaconda、Jupyter、包管理', 'VIDEO', '/videos/dataanalysis/chapter1.mp4', 35, 1, 'ACTIVE'),
(42, 41, 'Pandas基础', 'DataFrame、Series、数据读取', 'VIDEO', '/videos/dataanalysis/chapter2.mp4', 60, 2, 'ACTIVE'),
(43, 41, '数据清洗', '缺失值处理、异常值检测', 'VIDEO', '/videos/dataanalysis/chapter3.mp4', 55, 3, 'ACTIVE'),
(44, 41, '数据可视化', 'Matplotlib、Seaborn图表制作', 'VIDEO', '/videos/dataanalysis/chapter4.mp4', 65, 4, 'ACTIVE'),
(45, 41, '统计分析', '描述统计、相关分析、假设检验', 'VIDEO', '/videos/dataanalysis/chapter5.mp4', 70, 5, 'ACTIVE');

-- 插入课程注册数据
INSERT INTO course_enrollments (id, user_id, course_id, enrolled_at, completed_at, progress_percentage) VALUES
(1, 2, 1, '2025-07-01 09:00:00', '2025-07-15 16:30:00', 100.00),
(2, 2, 2, '2025-07-02 10:15:00', NULL, 75.50),
(3, 2, 12, '2025-07-03 14:20:00', NULL, 60.00),
(4, 2, 21, '2025-07-05 11:30:00', NULL, 40.00),
(5, 2, 31, '2025-07-08 13:45:00', NULL, 25.00),
(6, 8, 1, '2025-07-02 08:30:00', '2025-07-20 15:45:00', 100.00),
(7, 8, 3, '2025-07-04 09:15:00', NULL, 80.00),
(8, 8, 11, '2025-07-06 10:30:00', NULL, 90.00),
(9, 8, 12, '2025-07-07 14:00:00', NULL, 70.00),
(10, 8, 21, '2025-07-10 16:20:00', NULL, 30.00),
(11, 9, 2, '2025-07-03 11:00:00', NULL, 85.00),
(12, 9, 4, '2025-07-05 13:30:00', NULL, 65.00),
(13, 9, 12, '2025-07-08 15:45:00', NULL, 55.00),
(14, 9, 22, '2025-07-10 09:20:00', NULL, 45.00),
(15, 9, 41, '2025-07-12 11:15:00', NULL, 35.00),
(16, 10, 1, '2025-07-04 10:30:00', NULL, 95.00),
(17, 10, 5, '2025-07-06 14:15:00', NULL, 75.00),
(18, 10, 13, '2025-07-09 16:00:00', NULL, 50.00),
(19, 10, 23, '2025-07-11 12:45:00', NULL, 40.00),
(20, 10, 31, '2025-07-13 08:30:00', NULL, 20.00);

-- 插入学习记录数据
INSERT INTO learning_records (id, user_id, course_id, progress, status, start_time, completion_time, last_access_time) VALUES
(1, 2, 1, 100.00, 'COMPLETED', '2025-07-01 09:00:00', '2025-07-15 16:30:00', '2025-07-15 16:30:00'),
(2, 2, 2, 75.50, 'IN_PROGRESS', '2025-07-02 10:15:00', NULL, '2025-07-25 14:20:00'),
(3, 2, 12, 60.00, 'IN_PROGRESS', '2025-07-03 14:20:00', NULL, '2025-07-24 16:45:00'),
(4, 2, 21, 40.00, 'IN_PROGRESS', '2025-07-05 11:30:00', NULL, '2025-07-23 10:15:00'),
(5, 2, 31, 25.00, 'IN_PROGRESS', '2025-07-08 13:45:00', NULL, '2025-07-22 09:30:00'),
(6, 8, 1, 100.00, 'COMPLETED', '2025-07-02 08:30:00', '2025-07-20 15:45:00', '2025-07-20 15:45:00'),
(7, 8, 3, 80.00, 'IN_PROGRESS', '2025-07-04 09:15:00', NULL, '2025-07-25 11:20:00'),
(8, 8, 11, 90.00, 'IN_PROGRESS', '2025-07-06 10:30:00', NULL, '2025-07-24 13:15:00'),
(9, 8, 12, 70.00, 'IN_PROGRESS', '2025-07-07 14:00:00', NULL, '2025-07-23 15:30:00'),
(10, 8, 21, 30.00, 'IN_PROGRESS', '2025-07-10 16:20:00', NULL, '2025-07-22 12:45:00'),
(11, 9, 2, 85.00, 'IN_PROGRESS', '2025-07-03 11:00:00', NULL, '2025-07-25 10:30:00'),
(12, 9, 4, 65.00, 'IN_PROGRESS', '2025-07-05 13:30:00', NULL, '2025-07-24 14:45:00'),
(13, 9, 12, 55.00, 'IN_PROGRESS', '2025-07-08 15:45:00', NULL, '2025-07-23 16:20:00'),
(14, 9, 22, 45.00, 'IN_PROGRESS', '2025-07-10 09:20:00', NULL, '2025-07-22 11:15:00'),
(15, 9, 41, 35.00, 'IN_PROGRESS', '2025-07-12 11:15:00', NULL, '2025-07-21 13:30:00'),
(16, 10, 1, 95.00, 'IN_PROGRESS', '2025-07-04 10:30:00', NULL, '2025-07-25 09:45:00'),
(17, 10, 5, 75.00, 'IN_PROGRESS', '2025-07-06 14:15:00', NULL, '2025-07-24 12:30:00'),
(18, 10, 13, 50.00, 'IN_PROGRESS', '2025-07-09 16:00:00', NULL, '2025-07-23 14:15:00'),
(19, 10, 23, 40.00, 'IN_PROGRESS', '2025-07-11 12:45:00', NULL, '2025-07-22 16:00:00'),
(20, 10, 31, 20.00, 'IN_PROGRESS', '2025-07-13 08:30:00', NULL, '2025-07-21 10:45:00');

-- 插入用户行为数据 (推荐算法用)
INSERT INTO user_behaviors (id, user_id, course_id, behavior_type, behavior_value, created_at) VALUES
-- 用户2的行为数据
(1, 2, 1, 'VIEW', NULL, '2025-07-01 08:45:00'),
(2, 2, 1, 'ENROLL', NULL, '2025-07-01 09:00:00'),
(3, 2, 1, 'COMPLETE', NULL, '2025-07-15 16:30:00'),
(4, 2, 1, 'RATE', 4.5, '2025-07-15 16:35:00'),
(5, 2, 2, 'VIEW', NULL, '2025-07-02 09:30:00'),
(6, 2, 2, 'ENROLL', NULL, '2025-07-02 10:15:00'),
(7, 2, 12, 'VIEW', NULL, '2025-07-03 13:45:00'),
(8, 2, 12, 'ENROLL', NULL, '2025-07-03 14:20:00'),
(9, 2, 21, 'VIEW', NULL, '2025-07-05 11:00:00'),
(10, 2, 21, 'ENROLL', NULL, '2025-07-05 11:30:00'),
(11, 2, 31, 'VIEW', NULL, '2025-07-08 13:15:00'),
(12, 2, 31, 'ENROLL', NULL, '2025-07-08 13:45:00'),
(13, 2, 3, 'VIEW', NULL, '2025-07-10 15:20:00'),
(14, 2, 4, 'VIEW', NULL, '2025-07-12 10:30:00'),
(15, 2, 5, 'VIEW', NULL, '2025-07-14 14:15:00'),

-- 用户8的行为数据
(16, 8, 1, 'VIEW', NULL, '2025-07-02 08:00:00'),
(17, 8, 1, 'ENROLL', NULL, '2025-07-02 08:30:00'),
(18, 8, 1, 'COMPLETE', NULL, '2025-07-20 15:45:00'),
(19, 8, 1, 'RATE', 5.0, '2025-07-20 15:50:00'),
(20, 8, 3, 'VIEW', NULL, '2025-07-04 08:45:00'),
(21, 8, 3, 'ENROLL', NULL, '2025-07-04 09:15:00'),
(22, 8, 11, 'VIEW', NULL, '2025-07-06 10:00:00'),
(23, 8, 11, 'ENROLL', NULL, '2025-07-06 10:30:00'),
(24, 8, 12, 'VIEW', NULL, '2025-07-07 13:30:00'),
(25, 8, 12, 'ENROLL', NULL, '2025-07-07 14:00:00'),
(26, 8, 21, 'VIEW', NULL, '2025-07-10 15:45:00'),
(27, 8, 21, 'ENROLL', NULL, '2025-07-10 16:20:00'),
(28, 8, 2, 'VIEW', NULL, '2025-07-15 11:20:00'),
(29, 8, 4, 'VIEW', NULL, '2025-07-17 13:45:00'),
(30, 8, 13, 'VIEW', NULL, '2025-07-19 16:30:00'),

-- 用户9的行为数据
(31, 9, 2, 'VIEW', NULL, '2025-07-03 10:30:00'),
(32, 9, 2, 'ENROLL', NULL, '2025-07-03 11:00:00'),
(33, 9, 4, 'VIEW', NULL, '2025-07-05 13:00:00'),
(34, 9, 4, 'ENROLL', NULL, '2025-07-05 13:30:00'),
(35, 9, 12, 'VIEW', NULL, '2025-07-08 15:15:00'),
(36, 9, 12, 'ENROLL', NULL, '2025-07-08 15:45:00'),
(37, 9, 22, 'VIEW', NULL, '2025-07-10 08:45:00'),
(38, 9, 22, 'ENROLL', NULL, '2025-07-10 09:20:00'),
(39, 9, 41, 'VIEW', NULL, '2025-07-12 10:45:00'),
(40, 9, 41, 'ENROLL', NULL, '2025-07-12 11:15:00'),
(41, 9, 1, 'VIEW', NULL, '2025-07-16 14:20:00'),
(42, 9, 3, 'VIEW', NULL, '2025-07-18 16:45:00'),
(43, 9, 21, 'VIEW', NULL, '2025-07-20 12:30:00'),

-- 用户10的行为数据
(44, 10, 1, 'VIEW', NULL, '2025-07-04 10:00:00'),
(45, 10, 1, 'ENROLL', NULL, '2025-07-04 10:30:00'),
(46, 10, 5, 'VIEW', NULL, '2025-07-06 13:45:00'),
(47, 10, 5, 'ENROLL', NULL, '2025-07-06 14:15:00'),
(48, 10, 13, 'VIEW', NULL, '2025-07-09 15:30:00'),
(49, 10, 13, 'ENROLL', NULL, '2025-07-09 16:00:00'),
(50, 10, 23, 'VIEW', NULL, '2025-07-11 12:15:00'),
(51, 10, 23, 'ENROLL', NULL, '2025-07-11 12:45:00'),
(52, 10, 31, 'VIEW', NULL, '2025-07-13 08:00:00'),
(53, 10, 31, 'ENROLL', NULL, '2025-07-13 08:30:00'),
(54, 10, 2, 'VIEW', NULL, '2025-07-17 10:15:00'),
(55, 10, 12, 'VIEW', NULL, '2025-07-19 14:30:00'),
(56, 10, 21, 'VIEW', NULL, '2025-07-21 16:45:00'),

-- 其他用户的浏览行为
(57, 3, 1, 'VIEW', NULL, '2025-07-05 09:15:00'),
(58, 3, 21, 'VIEW', NULL, '2025-07-07 11:30:00'),
(59, 3, 31, 'VIEW', NULL, '2025-07-09 14:45:00'),
(60, 4, 2, 'VIEW', NULL, '2025-07-06 10:20:00'),
(61, 4, 12, 'VIEW', NULL, '2025-07-08 13:15:00'),
(62, 4, 22, 'VIEW', NULL, '2025-07-10 15:30:00'),
(63, 5, 3, 'VIEW', NULL, '2025-07-07 12:45:00'),
(64, 5, 13, 'VIEW', NULL, '2025-07-09 16:20:00'),
(65, 5, 23, 'VIEW', NULL, '2025-07-11 08:30:00'),
(66, 6, 4, 'VIEW', NULL, '2025-07-08 14:15:00'),
(67, 6, 14, 'VIEW', NULL, '2025-07-10 17:30:00'),
(68, 6, 24, 'VIEW', NULL, '2025-07-12 09:45:00'),
(69, 7, 5, 'VIEW', NULL, '2025-07-09 11:20:00'),
(70, 7, 15, 'VIEW', NULL, '2025-07-11 13:45:00'),
(71, 7, 25, 'VIEW', NULL, '2025-07-13 16:15:00');

-- 插入推荐数据
INSERT INTO recommendations (id, user_id, course_id, algorithm_type, score, reason, created_at) VALUES
-- 用户2的推荐
(1, 2, 3, 'COLLABORATIVE', 0.85, '基于相似用户的学习偏好', '2025-07-25 10:00:00'),
(2, 2, 4, 'CONTENT_BASED', 0.78, '与已学课程内容相关', '2025-07-25 10:00:00'),
(3, 2, 5, 'HYBRID', 0.82, '综合推荐算法', '2025-07-25 10:00:00'),
(4, 2, 11, 'COLLABORATIVE', 0.75, '相似用户也学习了此课程', '2025-07-25 10:00:00'),
(5, 2, 13, 'CONTENT_BASED', 0.72, '前端技术进阶课程', '2025-07-25 10:00:00'),
(6, 2, 22, 'HYBRID', 0.79, '后端开发相关推荐', '2025-07-25 10:00:00'),
(7, 2, 32, 'CONTENT_BASED', 0.68, '数据库技术扩展', '2025-07-25 10:00:00'),
(8, 2, 41, 'COLLABORATIVE', 0.73, '数据分析入门推荐', '2025-07-25 10:00:00'),

-- 用户8的推荐
(9, 8, 2, 'COLLABORATIVE', 0.88, '基于学习Java的用户偏好', '2025-07-25 10:00:00'),
(10, 8, 4, 'CONTENT_BASED', 0.81, '编程语言相关课程', '2025-07-25 10:00:00'),
(11, 8, 5, 'HYBRID', 0.84, 'Go语言实战推荐', '2025-07-25 10:00:00'),
(12, 8, 13, 'COLLABORATIVE', 0.76, '前端框架进阶', '2025-07-25 10:00:00'),
(13, 8, 15, 'CONTENT_BASED', 0.74, 'TypeScript技术栈', '2025-07-25 10:00:00'),
(14, 8, 22, 'HYBRID', 0.77, 'Python Web开发', '2025-07-25 10:00:00'),
(15, 8, 31, 'COLLABORATIVE', 0.71, '数据库设计课程', '2025-07-25 10:00:00'),
(16, 8, 42, 'CONTENT_BASED', 0.69, '机器学习入门', '2025-07-25 10:00:00'),

-- 用户9的推荐
(17, 9, 1, 'COLLABORATIVE', 0.83, 'Java基础推荐', '2025-07-25 10:00:00'),
(18, 9, 3, 'CONTENT_BASED', 0.76, 'C++程序设计', '2025-07-25 10:00:00'),
(19, 9, 11, 'HYBRID', 0.79, 'HTML5+CSS3基础', '2025-07-25 10:00:00'),
(20, 9, 13, 'COLLABORATIVE', 0.74, 'React开发进阶', '2025-07-25 10:00:00'),
(21, 9, 21, 'CONTENT_BASED', 0.72, 'Spring Boot微服务', '2025-07-25 10:00:00'),
(22, 9, 23, 'HYBRID', 0.77, 'Express.js API开发', '2025-07-25 10:00:00'),
(23, 9, 42, 'COLLABORATIVE', 0.70, '机器学习入门', '2025-07-25 10:00:00'),
(24, 9, 46, 'CONTENT_BASED', 0.68, '数据挖掘技术', '2025-07-25 10:00:00'),

-- 用户10的推荐
(25, 10, 2, 'COLLABORATIVE', 0.86, 'Python入门推荐', '2025-07-25 10:00:00'),
(26, 10, 3, 'CONTENT_BASED', 0.78, 'C++程序设计', '2025-07-25 10:00:00'),
(27, 10, 4, 'HYBRID', 0.81, 'JavaScript现代开发', '2025-07-25 10:00:00'),
(28, 10, 11, 'COLLABORATIVE', 0.75, 'HTML5+CSS3基础', '2025-07-25 10:00:00'),
(29, 10, 14, 'CONTENT_BASED', 0.73, 'Angular企业级开发', '2025-07-25 10:00:00'),
(30, 10, 22, 'HYBRID', 0.76, 'Django Web框架', '2025-07-25 10:00:00'),
(31, 10, 32, 'COLLABORATIVE', 0.71, 'PostgreSQL高级应用', '2025-07-25 10:00:00'),
(32, 10, 41, 'CONTENT_BASED', 0.69, 'Python数据分析', '2025-07-25 10:00:00');

-- 插入讨论主题数据
INSERT INTO discussion_topics (id, course_id, user_id, title, content, view_count, reply_count, last_reply_at, is_pinned, is_locked) VALUES
(1, 1, 2, 'Java开发环境配置问题', '在配置Java开发环境时遇到了一些问题，希望大家能帮忙解答。', 25, 3, '2025-07-16 14:30:00', FALSE, FALSE),
(2, 1, 8, 'Java集合框架最佳实践', '分享一些Java集合框架使用的最佳实践和性能优化技巧。', 42, 7, '2025-07-18 16:45:00', TRUE, FALSE),
(3, 2, 9, 'Python虚拟环境管理', '如何有效管理Python项目的虚拟环境？', 18, 2, '2025-07-17 11:20:00', FALSE, FALSE),
(4, 12, 2, 'Vue3组合式API vs 选项式API', 'Vue3中组合式API和选项式API的优缺点对比讨论。', 56, 12, '2025-07-20 15:30:00', TRUE, FALSE),
(5, 12, 8, 'Vue Router路由守卫使用技巧', '分享Vue Router中路由守卫的实际应用场景。', 33, 5, '2025-07-19 13:15:00', FALSE, FALSE),
(6, 21, 10, 'Spring Boot自动配置原理', 'Spring Boot自动配置的实现原理深度解析。', 67, 15, '2025-07-22 17:20:00', TRUE, FALSE),
(7, 31, 2, 'MySQL索引优化策略', 'MySQL数据库索引设计和优化的实战经验分享。', 45, 8, '2025-07-21 12:45:00', FALSE, FALSE),
(8, 41, 9, 'Pandas数据处理技巧', 'Pandas在数据清洗和处理中的高级技巧分享。', 29, 4, '2025-07-23 10:30:00', FALSE, FALSE);

-- 插入讨论回复数据
INSERT INTO discussion_replies (id, topic_id, user_id, content, parent_id, like_count) VALUES
(1, 1, 8, '可以尝试重新下载JDK，确保版本兼容性。', NULL, 5),
(2, 1, 10, '我也遇到过类似问题，检查一下环境变量配置。', NULL, 3),
(3, 1, 2, '谢谢大家的建议，问题已经解决了！', NULL, 2),
(4, 2, 2, '非常实用的分享，学到了很多！', NULL, 8),
(5, 2, 9, 'ArrayList和LinkedList的选择确实很重要。', NULL, 6),
(6, 2, 10, '性能测试的结果很有说服力。', NULL, 4),
(7, 3, 2, '推荐使用conda来管理Python环境。', NULL, 7),
(8, 3, 8, 'pipenv也是一个不错的选择。', NULL, 5),
(9, 4, 9, '组合式API在大型项目中确实更有优势。', NULL, 12),
(10, 4, 10, '但是学习成本相对较高。', NULL, 8),
(11, 4, 8, '可以逐步迁移，不用一次性全部改写。', NULL, 10),
(12, 5, 9, '路由守卫在权限控制中非常有用。', NULL, 6),
(13, 5, 2, 'beforeEach和beforeResolve的区别需要注意。', NULL, 4),
(14, 6, 2, '自动配置确实是Spring Boot的核心特性。', NULL, 15),
(15, 6, 8, '@EnableAutoConfiguration注解的工作原理很有趣。', NULL, 12),
(16, 6, 9, '条件注解的使用让配置更加灵活。', NULL, 9),
(17, 7, 8, '复合索引的顺序很关键。', NULL, 7),
(18, 7, 9, '覆盖索引可以显著提升查询性能。', NULL, 5),
(19, 8, 2, 'groupby操作确实很强大。', NULL, 6),
(20, 8, 10, 'merge函数在数据合并时很有用。', NULL, 4);

-- 显示数据统计
SELECT
    '用户总数' as 统计项, COUNT(*) as 数量 FROM users
UNION ALL
SELECT
    '课程总数' as 统计项, COUNT(*) as 数量 FROM courses
UNION ALL
SELECT
    '课程章节总数' as 统计项, COUNT(*) as 数量 FROM course_chapters
UNION ALL
SELECT
    '课程注册总数' as 统计项, COUNT(*) as 数量 FROM course_enrollments
UNION ALL
SELECT
    '学习记录总数' as 统计项, COUNT(*) as 数量 FROM learning_records
UNION ALL
SELECT
    '用户行为总数' as 统计项, COUNT(*) as 数量 FROM user_behaviors
UNION ALL
SELECT
    '推荐记录总数' as 统计项, COUNT(*) as 数量 FROM recommendations
UNION ALL
SELECT
    '讨论主题总数' as 统计项, COUNT(*) as 数量 FROM discussion_topics
UNION ALL
SELECT
    '讨论回复总数' as 统计项, COUNT(*) as 数量 FROM discussion_replies;
